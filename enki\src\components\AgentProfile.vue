<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Edit, ArrowLeft, Setting } from '@element-plus/icons-vue';
import { useAgentsStore } from '@/stores/agentsStore';
import DialogAgent from '@/components/DialogAgent.vue';
import type { Agent } from '@/types';

const route = useRoute();
const router = useRouter();
const agentsStore = useAgentsStore();

const agent = ref<Agent | null>(null);
const loading = ref(false);
const showEditDialog = ref(false);

const agentId = computed(() => route.params.agentId as string);

onMounted(async () => {
    await loadAgent();
});

const loadAgent = async () => {
    loading.value = true;
    try {
        // First try to find agent in store
        agent.value = agentsStore.getAgent(agentId.value) || null;
        
        // If not found in store, fetch all agents
        if (!agent.value) {
            await agentsStore.fetchAgents();
            agent.value = agentsStore.getAgent(agentId.value) || null;
        }
        
        if (!agent.value) {
            ElMessage.error('Agent not found');
            router.push('/contacts');
        }
    } catch (error) {
        ElMessage.error('Failed to load agent');
        console.error('Error loading agent:', error);
    } finally {
        loading.value = false;
    }
};

const handleEdit = () => {
    showEditDialog.value = true;
};

const handleBack = () => {
    router.push('/contacts');
};

const onEditDialogClose = () => {
    showEditDialog.value = false;
    // Refresh agent data after edit
    loadAgent();
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
};

const getStatusColor = (isActive: boolean) => {
    return isActive ? 'success' : 'info';
};

const getStatusText = (isActive: boolean) => {
    return isActive ? 'Active' : 'Inactive';
};

const formatTools = (tools: string[]) => {
    return tools.map(tool => 
        tool.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
    ).join(', ') || 'None';
};
</script>

<template>
    <div class="agent-profile-container">
        <div v-loading="loading" class="profile-content">
            <div class="profile-header">
                <el-button @click="handleBack" :icon="ArrowLeft" circle />
                <h1>Agent Profile</h1>
                <el-button
                    v-if="agent"
                    type="primary"
                    :icon="Edit"
                    @click="handleEdit"
                >
                    Edit
                </el-button>
            </div>

            <div v-if="agent" class="profile-details">
                <el-card class="profile-card">
                    <div class="profile-avatar-section">
                        <el-avatar :size="120" style="background-color: #409eff;">
                            <el-icon :size="60"><Setting /></el-icon>
                        </el-avatar>
                        <div class="profile-basic-info">
                            <h2>{{ agent.name }}</h2>
                            <p class="description">{{ agent.description || 'No description' }}</p>
                            <el-tag :type="getStatusColor(agent.is_active)" size="large">
                                {{ getStatusText(agent.is_active) }}
                            </el-tag>
                        </div>
                    </div>
                </el-card>

                <el-row :gutter="24" class="profile-sections">
                    <el-col :span="12">
                        <el-card>
                            <template #header>
                                <h3>Basic Information</h3>
                            </template>
                            <el-descriptions :column="1" border>
                                <el-descriptions-item label="Agent ID">
                                    {{ agent.id }}
                                </el-descriptions-item>
                                <el-descriptions-item label="Name">
                                    {{ agent.name }}
                                </el-descriptions-item>
                                <el-descriptions-item label="Description">
                                    {{ agent.description || 'Not set' }}
                                </el-descriptions-item>
                                <el-descriptions-item label="Created By">
                                    {{ agent.created_by }}
                                </el-descriptions-item>
                                <el-descriptions-item label="Status">
                                    <el-tag :type="getStatusColor(agent.is_active)">
                                        {{ getStatusText(agent.is_active) }}
                                    </el-tag>
                                </el-descriptions-item>
                            </el-descriptions>
                        </el-card>
                    </el-col>

                    <el-col :span="12">
                        <el-card>
                            <template #header>
                                <h3>Activity Information</h3>
                            </template>
                            <el-descriptions :column="1" border>
                                <el-descriptions-item label="Created At">
                                    {{ formatDate(agent.created_at) }}
                                </el-descriptions-item>
                                <el-descriptions-item label="Updated At">
                                    {{ formatDate(agent.updated_at) }}
                                </el-descriptions-item>
                            </el-descriptions>
                        </el-card>
                    </el-col>
                </el-row>

                <el-card class="parameters-section">
                    <template #header>
                        <h3>Configuration Parameters</h3>
                    </template>
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="Model Type">
                            <el-tag>{{ agent.parameters.model_type }}</el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="Temperature">
                            {{ agent.parameters.temperature || 'Not set' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="Max Tokens">
                            {{ agent.parameters.max_tokens || 'Not set' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="Tools">
                            {{ formatTools(agent.parameters.tools) }}
                        </el-descriptions-item>
                        <el-descriptions-item label="System Prompt" :span="2">
                            <div v-if="agent.parameters.system_prompt" class="system-prompt">
                                {{ agent.parameters.system_prompt }}
                            </div>
                            <span v-else>Not set</span>
                        </el-descriptions-item>
                    </el-descriptions>
                </el-card>

                <!-- Custom Configuration -->
                <el-card v-if="Object.keys(agent.parameters.custom_config).length > 0" class="custom-config-section">
                    <template #header>
                        <h3>Custom Configuration</h3>
                    </template>
                    <pre class="config-json">{{ JSON.stringify(agent.parameters.custom_config, null, 2) }}</pre>
                </el-card>

                <!-- Permissions Section (placeholder for future implementation) -->
                <el-card class="permissions-section">
                    <template #header>
                        <h3>Permissions</h3>
                    </template>
                    <el-empty description="Permissions management coming soon" />
                </el-card>
            </div>

            <div v-else-if="!loading" class="not-found">
                <el-empty description="Agent not found" />
            </div>
        </div>

        <!-- Edit Dialog -->
        <DialogAgent
            v-model="showEditDialog"
            :agent="agent || undefined"
            @close="onEditDialogClose"
        />
    </div>
</template>

<style scoped>
.agent-profile-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
}

.profile-header h1 {
    flex: 1;
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.profile-card {
    margin-bottom: 24px;
}

.profile-avatar-section {
    display: flex;
    align-items: center;
    gap: 24px;
    padding: 20px 0;
}

.profile-basic-info h2 {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
}

.profile-basic-info .description {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: var(--el-text-color-regular);
}

.profile-sections {
    margin-bottom: 24px;
}

.profile-sections .el-card {
    height: 100%;
}

.profile-sections h3,
.parameters-section h3,
.custom-config-section h3,
.permissions-section h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.parameters-section,
.custom-config-section,
.permissions-section {
    margin-bottom: 24px;
}

.system-prompt {
    white-space: pre-wrap;
    word-break: break-word;
    max-height: 200px;
    overflow-y: auto;
    padding: 8px;
    background-color: var(--el-fill-color-light);
    border-radius: 4px;
    font-family: monospace;
    font-size: 14px;
}

.config-json {
    background-color: var(--el-fill-color-light);
    padding: 16px;
    border-radius: 4px;
    font-size: 14px;
    overflow-x: auto;
    margin: 0;
}

.not-found {
    text-align: center;
    padding: 60px 20px;
}
</style>
