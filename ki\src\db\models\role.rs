use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

/// Role model for storing metadata (non-authorization data)
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct Role {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub color: Option<String>, // Hex color code for UI display
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// New role request
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct NewRole {
    pub name: String,
    pub description: Option<String>,
    pub color: Option<String>,
}

/// Update role request
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateRole {
    pub name: Option<String>,
    pub description: Option<String>,
    pub color: Option<String>,
}

/// Role response
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RoleResponse {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub color: Option<String>,
    pub created_at: String,
    pub updated_at: String,
}

impl From<Role> for RoleResponse {
    fn from(role: Role) -> Self {
        Self {
            id: role.id.to_string(),
            name: role.name,
            description: role.description,
            color: role.color,
            created_at: role.created_at.to_rfc3339(),
            updated_at: role.updated_at.to_rfc3339(),
        }
    }
}

/// Role tree node for hierarchy display
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleTreeNode {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub color: Option<String>,
    pub member_count: u64,
    pub children: Vec<RoleTreeNode>,
}

/// Role details with member information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleDetails {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub color: Option<String>,
    pub member_count: u64,
    pub members: Vec<RoleMember>,
    pub parent_id: Option<String>,
    pub children: Vec<RoleChild>,
    pub created_at: String,
    pub updated_at: String,
}

/// Role member information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleMember {
    pub user_id: String,
    pub display_name: Option<String>,
    pub email: String,
    pub photo_url: Option<String>,
}

/// Role child information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleChild {
    pub id: String,
    pub name: String,
    pub member_count: u64,
}

/// Set role parent request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SetRoleParentRequest {
    pub parent_id: Option<String>,
}

/// Bulk relationship operation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BulkRelationshipOperation {
    pub operation: RelationshipOperation,
    pub relationships: Vec<RelationshipTuple>,
}

/// Relationship operation type
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum RelationshipOperation {
    Create,
    Delete,
}

/// Relationship tuple
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RelationshipTuple {
    pub resource_type: String,
    pub resource_id: String,
    pub relation: String,
    pub subject_type: String,
    pub subject_id: String,
}

/// Permission check request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionCheckRequest {
    pub checks: Vec<PermissionCheck>,
}

/// Single permission check
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionCheck {
    pub resource_type: String,
    pub resource_id: String,
    pub permission: String,
    pub subject_type: String,
    pub subject_id: String,
}

/// Permission check response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionCheckResponse {
    pub results: Vec<PermissionCheckResult>,
}

/// Single permission check result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionCheckResult {
    pub resource_type: String,
    pub resource_id: String,
    pub permission: String,
    pub subject_type: String,
    pub subject_id: String,
    pub allowed: bool,
}
