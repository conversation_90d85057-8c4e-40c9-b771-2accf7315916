use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    Json,
};
use serde::Deserialize;
use uuid::Uuid;
use crate::{
    db::{
        models::{AgentResponse, NewAgent, UpdateAgent},
        repositories::AgentRepository,
    },
    ServerState,
};

#[derive(Deserialize)]
pub struct AgentQueryParams {
    active_only: Option<bool>,
    created_by: Option<String>,
}

/// Get all agents
pub async fn get_agents(
    State(server_state): State<ServerState>,
    Query(params): Query<AgentQueryParams>,
) -> Result<(StatusCode, Json<Vec<AgentResponse>>), (StatusCode, String)> {
    let repo = AgentRepository::new(server_state.db);

    let agents = if let Some(created_by) = params.created_by {
        repo.get_by_creator(&created_by).await
    } else if params.active_only.unwrap_or(false) {
        repo.get_active().await
    } else {
        repo.get_all().await
    };

    match agents {
        Ok(agents) => {
            let agent_responses: Vec<AgentResponse> = agents.into_iter().map(|agent| agent.into()).collect();
            Ok((StatusCode::OK, Json(agent_responses)))
        }
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get an agent by ID
pub async fn get_agent(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<(StatusCode, Json<AgentResponse>), (StatusCode, String)> {
    let repo = AgentRepository::new(server_state.db);

    let agent_id = Uuid::parse_str(&id)
        .map_err(|_| (StatusCode::BAD_REQUEST, "Invalid agent ID format".to_string()))?;

    match repo.get_by_id(agent_id).await {
        Ok(Some(agent)) => Ok((StatusCode::OK, Json(agent.into()))),
        Ok(None) => Err((StatusCode::NOT_FOUND, "Agent not found".to_string())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Create a new agent
pub async fn create_agent(
    State(server_state): State<ServerState>,
    Json(new_agent): Json<NewAgent>,
) -> Result<(StatusCode, Json<AgentResponse>), (StatusCode, String)> {
    let repo = AgentRepository::new(server_state.db);

    match repo.create(new_agent).await {
        Ok(agent) => Ok((StatusCode::CREATED, Json(agent.into()))),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Update an agent
pub async fn update_agent(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
    Json(update_agent): Json<UpdateAgent>,
) -> Result<(StatusCode, Json<AgentResponse>), (StatusCode, String)> {
    let repo = AgentRepository::new(server_state.db);

    let agent_id = Uuid::parse_str(&id)
        .map_err(|_| (StatusCode::BAD_REQUEST, "Invalid agent ID format".to_string()))?;

    match repo.update(agent_id, update_agent).await {
        Ok(agent) => Ok((StatusCode::OK, Json(agent.into()))),
        Err(e) => {
            if e.to_string().contains("not found") {
                Err((StatusCode::NOT_FOUND, "Agent not found".to_string()))
            } else {
                Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string()))
            }
        }
    }
}

/// Delete an agent
pub async fn delete_agent(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<StatusCode, (StatusCode, String)> {
    let repo = AgentRepository::new(server_state.db);

    let agent_id = Uuid::parse_str(&id)
        .map_err(|_| (StatusCode::BAD_REQUEST, "Invalid agent ID format".to_string()))?;

    match repo.delete(agent_id).await {
        Ok(()) => Ok(StatusCode::NO_CONTENT),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}
