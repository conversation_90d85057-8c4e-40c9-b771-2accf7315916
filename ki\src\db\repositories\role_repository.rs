use anyhow::Result;
use uuid::Uuid;
use crate::db::{Database, DatabaseConnection, models::{Role, NewRole, UpdateRole}};

/// Repository for role metadata operations
pub struct RoleRepository {
    db: Database,
}

impl RoleRepository {
    pub fn new(db: Database) -> Self {
        Self { db }
    }

    /// Create a new role
    pub async fn create(&self, new_role: NewRole) -> Result<Role> {
        let id = Uuid::new_v4();

        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let role = sqlx::query_as::<_, Role>(
                    r#"
                    INSERT INTO roles (id, name, description, color)
                    VALUES (?, ?, ?, ?)
                    RETURNING id, name, description, color, created_at, updated_at
                    "#
                )
                .bind(id)
                .bind(&new_role.name)
                .bind(&new_role.description)
                .bind(&new_role.color)
                .fetch_one(pool)
                .await?;

                Ok(role)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let role = sqlx::query_as::<_, Role>(
                    r#"
                    INSERT INTO roles (id, name, description, color)
                    VALUES ($1, $2, $3, $4)
                    RETURNING id, name, description, color, created_at, updated_at
                    "#
                )
                .bind(id)
                .bind(&new_role.name)
                .bind(&new_role.description)
                .bind(&new_role.color)
                .fetch_one(pool)
                .await?;

                Ok(role)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get all roles
    pub async fn get_all(&self) -> Result<Vec<Role>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let roles = sqlx::query_as::<_, Role>(
                    r#"
                    SELECT id, name, description, color, created_at, updated_at
                    FROM roles
                    ORDER BY name
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(roles)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let roles = sqlx::query_as::<_, Role>(
                    r#"
                    SELECT id, name, description, color, created_at, updated_at
                    FROM roles
                    ORDER BY name
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(roles)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get role by ID
    pub async fn get_by_id(&self, id: Uuid) -> Result<Option<Role>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let role = sqlx::query_as::<_, Role>(
                    r#"
                    SELECT id, name, description, color, created_at, updated_at
                    FROM roles
                    WHERE id = ?
                    "#
                )
                .bind(id)
                .fetch_optional(pool)
                .await?;

                Ok(role)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let role = sqlx::query_as::<_, Role>(
                    r#"
                    SELECT id, name, description, color, created_at, updated_at
                    FROM roles
                    WHERE id = $1
                    "#
                )
                .bind(id)
                .fetch_optional(pool)
                .await?;

                Ok(role)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Update role
    pub async fn update(&self, id: Uuid, update_role: UpdateRole) -> Result<Option<Role>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                // Build dynamic update query
                let mut query_parts = vec![];
                let mut bind_values: Vec<Box<dyn sqlx::Encode<'_, sqlx::Sqlite> + Send + Sync>> = vec![];

                if let Some(name) = &update_role.name {
                    query_parts.push("name = ?");
                    bind_values.push(Box::new(name.clone()));
                }
                if let Some(description) = &update_role.description {
                    query_parts.push("description = ?");
                    bind_values.push(Box::new(description.clone()));
                }
                if let Some(color) = &update_role.color {
                    query_parts.push("color = ?");
                    bind_values.push(Box::new(color.clone()));
                }

                if query_parts.is_empty() {
                    return self.get_by_id(id).await;
                }

                let query = format!(
                    "UPDATE roles SET {} WHERE id = ? RETURNING id, name, description, color, created_at, updated_at",
                    query_parts.join(", ")
                );

                let mut query_builder = sqlx::query_as::<_, Role>(&query);
                for value in bind_values {
                    // Note: This is a simplified approach. In practice, you'd need proper type handling
                    // For now, we'll use a simpler approach with individual field updates
                }
                query_builder = query_builder.bind(id);

                // For simplicity, let's use individual field updates
                if let Some(name) = update_role.name {
                    sqlx::query("UPDATE roles SET name = ? WHERE id = ?")
                        .bind(&name)
                        .bind(id)
                        .execute(pool)
                        .await?;
                }
                if let Some(description) = update_role.description {
                    sqlx::query("UPDATE roles SET description = ? WHERE id = ?")
                        .bind(&description)
                        .bind(id)
                        .execute(pool)
                        .await?;
                }
                if let Some(color) = update_role.color {
                    sqlx::query("UPDATE roles SET color = ? WHERE id = ?")
                        .bind(&color)
                        .bind(id)
                        .execute(pool)
                        .await?;
                }

                self.get_by_id(id).await
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                // Similar implementation for PostgreSQL
                if let Some(name) = update_role.name {
                    sqlx::query!("UPDATE roles SET name = $1 WHERE id = $2", name, id)
                        .execute(pool)
                        .await?;
                }
                if let Some(description) = update_role.description {
                    sqlx::query!("UPDATE roles SET description = $1 WHERE id = $2", description, id)
                        .execute(pool)
                        .await?;
                }
                if let Some(color) = update_role.color {
                    sqlx::query!("UPDATE roles SET color = $1 WHERE id = $2", color, id)
                        .execute(pool)
                        .await?;
                }

                self.get_by_id(id).await
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Delete role
    pub async fn delete(&self, id: Uuid) -> Result<bool> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let result = sqlx::query("DELETE FROM roles WHERE id = ?")
                    .bind(id)
                    .execute(pool)
                    .await?;

                Ok(result.rows_affected() > 0)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let result = sqlx::query!("DELETE FROM roles WHERE id = $1", id)
                    .execute(pool)
                    .await?;

                Ok(result.rows_affected() > 0)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Check if role exists by name
    pub async fn exists_by_name(&self, name: &str) -> Result<bool> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let exists: bool = sqlx::query_scalar(
                    "SELECT EXISTS(SELECT 1 FROM roles WHERE name = ?)"
                )
                .bind(name)
                .fetch_one(pool)
                .await?;

                Ok(exists)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let exists = sqlx::query!(
                    "SELECT EXISTS(SELECT 1 FROM roles WHERE name = $1) as \"exists!\"",
                    name
                )
                .fetch_one(pool)
                .await?
                .exists;

                Ok(exists)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }
}
