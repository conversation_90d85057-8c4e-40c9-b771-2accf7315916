<script setup lang="ts">
import { ref, computed, type PropType } from 'vue';
import { useKanbanStore } from '@/stores/kanbanStore';
import { useUsersStore } from '@/stores/usersStore';
import { useAgentsStore } from '@/stores/agentsStore';
import Subtasks from '@/components/Subtasks.vue';
import type { Optional, Task } from '@/types';
import { User as UserIcon, Setting } from '@element-plus/icons-vue';

const props = defineProps({
    task: Object as PropType<Optional<Task, 'id'>>
});

const emit = defineEmits(['save']);

const kanbanStore = useKanbanStore();
const usersStore = useUsersStore();
const agentsStore = useAgentsStore();

const taskForm = ref<Omit<Task, 'id' | 'position'>>({
	name: '',
	description: '',
	status: 'todo',
	tags: [] as string[],
	assignees: [] as string[],
	start_time: undefined,
	end_time: undefined,
    parent_task_id: undefined,
    blocking: [] as string[],
    blocked_by: [] as string[],
});

const columns = computed(() => kanbanStore.columns);
const show = computed({
    get: () => {
        if (!props.task) return false;
        taskForm.value = props.task;
        return true;
    },
    set: (_: boolean) => { return; }
});

const parent = computed({
    get: () => kanbanStore.tasks.find(t => t.id === taskForm.value.parent_task_id)?.name,
    set: (value) => { 
        taskForm.value.parent_task_id = value;
    }
});

// Get subtasks recursively
type TaskNode = {
    value: string;
    label: string;
    children?: TaskNode[];
};

function getSubTasks(parentTaskId: string): TaskNode[] {
    return kanbanStore.tasks
        .filter(task => task.id !== props.task?.id)
        .filter(task => task.parent_task_id === parentTaskId)
        .map(subtask => {
            return {
                value: subtask.id,
                label: subtask.name,
                children: getSubTasks(subtask.id),
            }
        });
}

function getTaskTree(): TaskNode[] {
    return kanbanStore.getProjects
        .filter(project => project.id !== props.task?.id)
        .map(project => {
            return {
                value: project.id,
                label: project.name,
                children: getSubTasks(project.id),
            }
        });
}

async function saveTask() {
	if (!props.task || !taskForm.value.name.trim() || !taskForm.value.status) {
		return;
	}

	const taskDataPayload = {
		name: taskForm.value.name,
		description: taskForm.value.description,
		status: taskForm.value.status,
		position: props.task.position,
		tags: taskForm.value.tags,
		assignees: taskForm.value.assignees,
		start_time: taskForm.value.start_time ? new Date(taskForm.value.start_time).toISOString() : undefined,
		end_time: taskForm.value.end_time ? new Date(taskForm.value.end_time).toISOString() : undefined,
        parent_task_id: taskForm.value.parent_task_id,
        blocking: taskForm.value.blocking,
        blocked_by: taskForm.value.blocked_by,
	};

    let returnedTask = undefined;
	if (props.task.id) {
		await kanbanStore.updateTask({ id: props.task.id, ...taskDataPayload });
	} else {
		returnedTask = await kanbanStore.createTask(taskDataPayload);
	}

    emit('save', returnedTask);
}
</script>

<template>
    <el-dialog v-model="show" :title="task ? (task.parent_task_id === undefined ? 'Edit Project' : 'Edit Task') : 'Add New Task'" width="80%" @close="() => emit('save', undefined)">
        <el-form :model="taskForm" label-width="120px">
            <el-form-item :label="task?.parent_task_id === undefined ? 'Project Name' : 'Task Name'" required>
                <el-input v-model="taskForm.name" type="textarea" :rows="3" :placeholder="task?.parent_task_id === undefined ? 'Enter project name' : 'Enter task name'" />
            </el-form-item>
            <el-form-item label="Description">
                <el-input v-model="taskForm.description" type="textarea" :rows="3"
                    :placeholder="task?.parent_task_id === undefined ? 'Enter project description' : 'Enter task description'" />
            </el-form-item>
            <el-form-item label="Status">
                <el-select v-model="taskForm.status" placeholder="Select status">
                    <el-option v-for="column in columns" :key="column.id" :label="column.name" :value="column.id" />
                </el-select>
            </el-form-item>
            <el-form-item label="Tags">
                <el-select v-model="taskForm.tags" multiple filterable allow-create default-first-option
                    placeholder="Enter or select tags">
                </el-select>
            </el-form-item>
            <el-form-item label="Assignees">
                <el-select v-model="taskForm.assignees" multiple filterable placeholder="Select assignees">
                    <el-option-group label="Users">
                        <el-option
                            v-for="user in usersStore.users"
                            :key="`user-${user.id}`"
                            :label="user.display_name ?? user.email"
                            :value="user.id"
                        >
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <el-avatar :src="user.photo_url" :size="20">
                                    <el-icon><UserIcon /></el-icon>
                                </el-avatar>
                                <span>{{ user.display_name ?? user.email }}</span>
                            </div>
                        </el-option>
                    </el-option-group>
                    <el-option-group label="Agents">
                        <el-option
                            v-for="agent in agentsStore.getActiveAgents"
                            :key="`agent-${agent.id}`"
                            :label="agent.name"
                            :value="agent.id"
                        >
                            <div style="display: flex; align-items: center; gap: 8px;">
                                <el-avatar :size="20" style="background-color: #409eff;">
                                    <el-icon><Setting /></el-icon>
                                </el-avatar>
                                <span>{{ agent.name }}</span>
                            </div>
                        </el-option>
                    </el-option-group>
                </el-select>
            </el-form-item>
            <el-form-item label="Parent Task" v-if="task?.id">
                <el-tree-select
                    check-strictly
                    v-model="parent"
                    :data="getTaskTree()"
                />
            </el-form-item>
            <el-form-item label="Blocking" v-if="task?.id">
                <el-tree-select
                    multiple
                    check-strictly
                    v-model="taskForm.blocking"
                    :data="getTaskTree()"
                    placeholder="Select tasks that this task blocks"
                />
            </el-form-item>
            <el-form-item label="Blocked By" v-if="task?.id">
                <el-tree-select
                    multiple
                    check-strictly
                    v-model="taskForm.blocked_by"
                    :data="getTaskTree()"
                    placeholder="Select tasks that block this task"
                />
            </el-form-item>
            <el-form-item label="Sub-Tasks" v-if="task?.id">
                <Subtasks :taskId="task.id"/>
            </el-form-item>
            <el-form-item label="Start Time">
                <el-date-picker v-model="taskForm.start_time" type="datetime"
                    placeholder="Select start date and time" />
            </el-form-item>
            <el-form-item label="End Time">
                <el-date-picker v-model="taskForm.end_time" type="datetime"
                    placeholder="Select end date and time" />
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="() => emit('save', undefined)">Cancel</el-button>
                <el-button type="primary" @click="saveTask"
                    :disabled="!taskForm.name.trim() || !taskForm.status">Save</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<style scoped>
:deep(.el-form-item__content > *) {
    width: 100%;
}
</style>