<script setup lang="ts">
import { ref, computed, inject, type Ref } from 'vue';
import { ElMessage } from 'element-plus';
import { useAgentsStore } from '@/stores/agentsStore';
import type { Agent, AgentParameters } from '@/types';
import type { User as FirebaseUser } from 'firebase/auth';

const props = defineProps<{
    agent?: Agent;
}>();

const emit = defineEmits<{
    'close': [];
}>();

const agentsStore = useAgentsStore();
const user = inject<Ref<FirebaseUser | null>>('user') as Ref<FirebaseUser | null>;

const agentForm = ref({
    name: '',
    description: '',
    parameters: {
        model_type: 'claude-3-sonnet',
        temperature: 0.7,
        max_tokens: 4096,
        system_prompt: '',
        tools: [] as string[],
        custom_config: {}
    } as AgentParameters,
    is_active: true
});

const formRef = ref();
const loading = ref(false);

const show = computed({
    get: () => {
        if (!props.agent) return false;
        agentForm.value = {
            name: props.agent.name,
            description: props.agent.description || '',
            parameters: {
                model_type: props.agent.parameters.model_type,
                temperature: props.agent.parameters.temperature || 0.7,
                max_tokens: props.agent.parameters.max_tokens || 4096,
                system_prompt: props.agent.parameters.system_prompt || '',
                tools: [...(props.agent.parameters.tools || [])],
                custom_config: { ...props.agent.parameters.custom_config }
            },
            is_active: props.agent.is_active
        };
        return true;
    },
    set: (_) => resetForm()
});

const isEditing = computed(() => !!props.agent);
const dialogTitle = computed(() => isEditing.value ? 'Edit Agent' : 'Add New Agent');

const modelOptions = [
    { label: 'Claude 3 Sonnet', value: 'claude-3-sonnet' },
    { label: 'Claude 3 Haiku', value: 'claude-3-haiku' },
    { label: 'Claude 3 Opus', value: 'claude-3-opus' },
    { label: 'GPT-4', value: 'gpt-4' },
    { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' }
];

const availableTools = [
    'web_search',
    'code_execution',
    'file_operations',
    'image_generation',
    'data_analysis'
];

const rules = {
    name: [
        { required: true, message: 'Agent name is required', trigger: 'blur' }
    ],
    'parameters.model_type': [
        { required: true, message: 'Model type is required', trigger: 'change' }
    ]
};

const resetForm = () => {
    agentForm.value = {
        name: '',
        description: '',
        parameters: {
            model_type: 'claude-3-sonnet',
            temperature: 0.7,
            max_tokens: 4096,
            system_prompt: '',
            tools: [],
            custom_config: {}
        },
        is_active: true
    };
    formRef.value?.clearValidate();
};

const handleClose = () => {
    resetForm();
    emit('close');
};

const handleSave = async () => {
    if (!formRef.value || !user.value) return;

    try {
        await formRef.value.validate();
        loading.value = true;

        const agentData = {
            name: agentForm.value.name,
            description: agentForm.value.description || undefined,
            parameters: agentForm.value.parameters,
            created_by: user.value.uid,
            is_active: agentForm.value.is_active
        };

        if (isEditing.value && props.agent) {
            await agentsStore.updateAgent(props.agent.id, agentData);
            ElMessage.success('Agent updated successfully');
        } else {
            await agentsStore.createAgent(agentData);
            ElMessage.success('Agent created successfully');
        }

        handleClose();
    } catch (error) {
        console.error('Error saving agent:', error);
        ElMessage.error(isEditing.value ? 'Failed to update agent' : 'Failed to create agent');
    } finally {
        loading.value = false;
    }
};
</script>

<template>
    <el-dialog
        v-model="show"
        :title="dialogTitle"
        width="600px"
        @close="handleClose"
    >
        <el-form
            ref="formRef"
            :model="agentForm"
            :rules="rules"
            label-width="120px"
            label-position="left"
        >
            <el-form-item label="Name" prop="name">
                <el-input
                    v-model="agentForm.name"
                    placeholder="Enter agent name"
                />
            </el-form-item>

            <el-form-item label="Description">
                <el-input
                    v-model="agentForm.description"
                    type="textarea"
                    :rows="3"
                    placeholder="Enter agent description (optional)"
                />
            </el-form-item>

            <el-form-item label="Model Type" prop="parameters.model_type">
                <el-select
                    v-model="agentForm.parameters.model_type"
                    placeholder="Select model type"
                    style="width: 100%"
                >
                    <el-option
                        v-for="option in modelOptions"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                    />
                </el-select>
            </el-form-item>

            <el-form-item label="Temperature">
                <el-slider
                    v-model="agentForm.parameters.temperature"
                    :min="0"
                    :max="2"
                    :step="0.1"
                    show-input
                    :show-input-controls="false"
                />
                <div class="form-help-text">
                    Controls randomness: 0 = deterministic, 2 = very random
                </div>
            </el-form-item>

            <el-form-item label="Max Tokens">
                <el-input-number
                    v-model="agentForm.parameters.max_tokens"
                    :min="1"
                    :max="32000"
                    :step="100"
                    style="width: 100%"
                />
                <div class="form-help-text">
                    Maximum number of tokens in the response
                </div>
            </el-form-item>

            <el-form-item label="System Prompt">
                <el-input
                    v-model="agentForm.parameters.system_prompt"
                    type="textarea"
                    :rows="4"
                    placeholder="Enter system prompt to define agent behavior (optional)"
                />
            </el-form-item>

            <el-form-item label="Tools">
                <el-checkbox-group v-model="agentForm.parameters.tools">
                    <el-checkbox
                        v-for="tool in availableTools"
                        :key="tool"
                        :label="tool"
                        :value="tool"
                    >
                        {{ tool.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) }}
                    </el-checkbox>
                </el-checkbox-group>
            </el-form-item>

            <el-form-item label="Status">
                <el-switch
                    v-model="agentForm.is_active"
                    active-text="Active"
                    inactive-text="Inactive"
                />
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">Cancel</el-button>
                <el-button
                    type="primary"
                    :loading="loading"
                    @click="handleSave"
                >
                    {{ isEditing ? 'Update' : 'Create' }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped>
.form-help-text {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}
</style>
