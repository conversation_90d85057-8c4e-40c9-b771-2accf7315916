{"rustc": 10895048813736897673, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2241668132362809309, "path": 16046190976464274511, "deps": [[1988483478007900009, "unicode_ident", false, 3559815257268929577], [12410540580958238005, "proc_macro2", false, 17053340830393542494], [17990358020177143287, "quote", false, 12172367782111389661]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-bb8d04fe2b356b47\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}