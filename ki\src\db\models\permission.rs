use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

/// Entity type for permissions
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, sqlx::Type)]
#[sqlx(rename_all = "lowercase")]
pub enum EntityType {
    #[serde(rename = "user")]
    User,
    #[serde(rename = "agent")]
    Agent,
}

impl From<&str> for EntityType {
    fn from(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "agent" => Self::Agent,
            _ => Self::User,
        }
    }
}

/// Resource type for permissions
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, sqlx::Type)]
#[sqlx(rename_all = "lowercase")]
pub enum ResourceType {
    #[serde(rename = "task")]
    Task,
    #[serde(rename = "project")]
    Project,
    #[serde(rename = "user")]
    User,
    #[serde(rename = "agent")]
    Agent,
    #[serde(rename = "system")]
    System,
}

impl From<&str> for ResourceType {
    fn from(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "task" => Self::Task,
            "project" => Self::Project,
            "user" => Self::User,
            "agent" => Self::Agent,
            "system" => Self::System,
            _ => Self::Task,
        }
    }
}

/// Permission level
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, sqlx::Type)]
#[sqlx(rename_all = "lowercase")]
pub enum PermissionLevel {
    #[serde(rename = "read")]
    Read,
    #[serde(rename = "write")]
    Write,
    #[serde(rename = "admin")]
    Admin,
}

impl From<&str> for PermissionLevel {
    fn from(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "write" => Self::Write,
            "admin" => Self::Admin,
            _ => Self::Read,
        }
    }
}

/// Permission model
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Permission {
    pub id: Uuid,
    pub entity_type: EntityType,
    pub entity_id: String, // User ID or Agent ID
    pub resource_type: ResourceType,
    pub resource_id: Option<String>, // Specific resource ID, None for global permissions
    pub permission_level: PermissionLevel,
    pub granted_by: String, // Firebase user ID who granted this permission
    pub created_at: DateTime<Utc>,
}

/// New permission request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewPermission {
    pub entity_type: EntityType,
    pub entity_id: String,
    pub resource_type: ResourceType,
    pub resource_id: Option<String>,
    pub permission_level: PermissionLevel,
    pub granted_by: String,
}

/// Update permission request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdatePermission {
    pub permission_level: PermissionLevel,
}

/// Permission response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionResponse {
    pub id: String,
    pub entity_type: String,
    pub entity_id: String,
    pub resource_type: String,
    pub resource_id: Option<String>,
    pub permission_level: String,
    pub granted_by: String,
    pub created_at: String,
}

impl From<Permission> for PermissionResponse {
    fn from(permission: Permission) -> Self {
        Self {
            id: permission.id.to_string(),
            entity_type: match permission.entity_type {
                EntityType::User => "user".to_string(),
                EntityType::Agent => "agent".to_string(),
            },
            entity_id: permission.entity_id,
            resource_type: match permission.resource_type {
                ResourceType::Task => "task".to_string(),
                ResourceType::Project => "project".to_string(),
                ResourceType::User => "user".to_string(),
                ResourceType::Agent => "agent".to_string(),
                ResourceType::System => "system".to_string(),
            },
            resource_id: permission.resource_id,
            permission_level: match permission.permission_level {
                PermissionLevel::Read => "read".to_string(),
                PermissionLevel::Write => "write".to_string(),
                PermissionLevel::Admin => "admin".to_string(),
            },
            granted_by: permission.granted_by,
            created_at: permission.created_at.to_rfc3339(),
        }
    }
}
