import { defineStore } from 'pinia';
import type { Column, Task, TaskStatus } from '@/types';
import * as kiApi from '@/services/kiApi';

interface KanbanState {
  columns: Column[];
  tasks: Task[];
  currentProjectId: string;
  serverUrl: string;
  loading: boolean;
  error: string | null;
}

export const useKanbanStore = defineStore('kanban', {
  state: (): KanbanState => ({
    columns: [
      { id: "todo", name: 'To Do' },
      { id: "doing", name: 'Doing' },
      { id: "review", name: 'Review' },
      { id: "done", name: 'Done' },
    ],
    tasks: [],
    currentProjectId: '',
    serverUrl: '',
    loading: false,
    error: null,
  }),

  getters: {
    getTasksByStatus: (state) => (parent_task_id: string, status: TaskStatus) => {
      return state.tasks
        .filter(task => task.parent_task_id == parent_task_id && task.status === status)
        .sort((a, b) => a.position - b.position);
    },

    getCurrentProject: (state) => {
      return state.tasks.find(task => task.id === state.currentProjectId);
    },

    getTaskHierarchy: (state) => (taskId: string): Task[] => {
      const task = state.tasks.find(t => t.id === taskId);
      if (!task) return [];
      const hierarchy: Task[] = [task];
      let currentTask: Task = task;
      while (currentTask?.parent_task_id) {
        const parent = state.tasks.find(t => t.id === currentTask.parent_task_id);
        if (parent) {
          hierarchy.push(parent);
          currentTask = parent;
        } else {
          break;
        }
      }
      return hierarchy.reverse();
    },

    getSubTasks: (state) => (taskId: string): Task[] => {
      return state.tasks.filter(t => t.parent_task_id === taskId);
    },

    getProjects: (state) => {
      return state.tasks.filter(t => t.parent_task_id === null);
    },
  },

  actions: {
    // Project actions
    async loadProjects(url: string) {
      this.serverUrl = url;
      this.loading = true;
      this.error = null;

      try {
        this.tasks = await kiApi.getAllTasks(this.serverUrl);
        const projects = this.getProjects;

        if (projects.length > 0 && !this.currentProjectId) {
          this.currentProjectId = projects[0].id;
        }
      } catch (error: any) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    async setCurrentProject(projectId: string) {
      if (this.tasks.some(p => p.id === projectId)) {
        this.error = null;
        this.currentProjectId = projectId;
      } else {
        this.error = `Project with ID ${projectId} not found`;
      }
    },

    async createTask(taskData: Omit<Task, 'id'>) {
      this.loading = true;
      this.error = null;
      let task = undefined;

      try {
        task = await kiApi.createTask(
          this.serverUrl,
          taskData
        );
        this.tasks.push(task);
      } catch (error: any) {
        this.error = error.message;
      } finally {
        this.loading = false;
        return task;
      }
    },

    async updateTask(task: Task) {
      this.loading = true;
      this.error = null;

      try {
        const response = await kiApi.updateTask(
          this.serverUrl,
          task
        );

        // Update the primary task in the local state
        const primaryIndex = this.tasks.findIndex(t => t.id === task.id);
        if (primaryIndex !== -1) {
          this.tasks[primaryIndex] = response.primary_task;
        }

        // Update all affected tasks in the local state
        for (const affectedTask of response.affected_tasks) {
          const affectedIndex = this.tasks.findIndex(t => t.id === affectedTask.id);
          if (affectedIndex !== -1) {
            this.tasks[affectedIndex] = affectedTask;
          }
        }
      } catch (error: any) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    async deleteTask(taskId: string) {
      this.loading = true;
      this.error = null;

      try {
        await kiApi.deleteTask(this.serverUrl, taskId);

        // Remove the task from the local state
        const index = this.tasks.findIndex(t => t.id === taskId);
        if (index !== -1) {
          this.tasks.splice(index, 1);
        }
      } catch (error: any) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    },

    async moveTask(taskId: string, newStatus: TaskStatus, newPosition: number) {
      this.loading = true;
      this.error = null;

      try {
        const updatedTask = await kiApi.moveTask(
          this.serverUrl,
          taskId,
          newStatus,
          newPosition
        );

        // Update the task in the local state
        const index = this.tasks.findIndex(t => t.id === taskId);
        if (index !== -1) {
          this.tasks[index] = updatedTask;
        }
      } catch (error: any) {
        this.error = error.message;
      } finally {
        this.loading = false;
      }
    }
  }
});
