use anyhow::Result;
use sqlx::Pool;
use sqlx::Sqlite;
#[cfg(feature = "postgres")]
use sqlx::postgres::PgPool;
use tracing::info;

/// Initialize SQLite database schema
#[cfg(feature = "sqlite")]
pub async fn init_sqlite_schema(pool: &Pool<Sqlite>) -> Result<()> {
    info!("Initializing SQLite database schema");

    // Create tasks table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS tasks (
            id UUID PRIMARY KEY NOT NULL,
            name TEXT NOT NULL,
            status TEXT NOT NULL CHECK (status IN ('todo', 'doing', 'review', 'done')),
            description TEXT,
            tags TEXT NOT NULL DEFAULT '[]',
            assignees TEXT NOT NULL DEFAULT '[]',
            start_time TIMESTAMP,
            end_time TIMESTAMP,
            parent_task_id UUID,
            position INTEGER NOT NULL DEFAULT 0,
            blocking TEXT NOT NULL DEFAULT '[]',
            blocked_by TEXT NOT NULL DEFAULT '[]',
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_task_id) REFERENCES tasks(id) ON DELETE SET NULL
        )
        "#
    )
    .execute(pool)
    .await?;
    info!("Created tasks table");

    // Create sessions table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS sessions (
            id UUID PRIMARY KEY NOT NULL,
            user_id TEXT NOT NULL,
            token TEXT NOT NULL UNIQUE,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        "#
    )
    .execute(pool)
    .await?;
    info!("Created sessions table");

    // Create users table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS users (
            id UUID PRIMARY KEY NOT NULL,
            user_id TEXT NOT NULL UNIQUE,
            email TEXT NOT NULL,
            display_name TEXT,
            photo_url TEXT,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )
        "#
    )
    .execute(pool)
    .await?;
    info!("Created users table");

    // Create agents table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS agents (
            id UUID PRIMARY KEY NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            parameters TEXT NOT NULL DEFAULT '{}',
            created_by TEXT NOT NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN NOT NULL DEFAULT TRUE
        )
        "#
    )
    .execute(pool)
    .await?;
    info!("Created agents table");

    // Create permissions table
    sqlx::query(
        r#"
        CREATE TABLE IF NOT EXISTS permissions (
            id UUID PRIMARY KEY NOT NULL,
            entity_type TEXT NOT NULL CHECK (entity_type IN ('user', 'agent')),
            entity_id TEXT NOT NULL,
            resource_type TEXT NOT NULL CHECK (resource_type IN ('task', 'project', 'user', 'agent', 'system')),
            resource_id TEXT,
            permission_level TEXT NOT NULL CHECK (permission_level IN ('read', 'write', 'admin')),
            granted_by TEXT NOT NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
        )
        "#
    )
    .execute(pool)
    .await?;
    info!("Created permissions table");

    // Create indexes
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_tasks_parent_task_id ON tasks(parent_task_id)")
        .execute(pool)
        .await?;
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)")
        .execute(pool)
        .await?;
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(token)")
        .execute(pool)
        .await?;
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)")
        .execute(pool)
        .await?;
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_users_user_id ON users(user_id)")
        .execute(pool)
        .await?;
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")
        .execute(pool)
        .await?;
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_agents_created_by ON agents(created_by)")
        .execute(pool)
        .await?;
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_agents_is_active ON agents(is_active)")
        .execute(pool)
        .await?;
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_permissions_entity ON permissions(entity_type, entity_id)")
        .execute(pool)
        .await?;
    sqlx::query("CREATE INDEX IF NOT EXISTS idx_permissions_resource ON permissions(resource_type, resource_id)")
        .execute(pool)
        .await?;
    info!("Created indexes");

    // Create triggers
    sqlx::query(
        r#"
        CREATE TRIGGER IF NOT EXISTS trig_tasks_updated_at
        AFTER UPDATE ON tasks
        FOR EACH ROW
        BEGIN
            UPDATE tasks SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
        END
        "#
    )
    .execute(pool)
    .await?;

    sqlx::query(
        r#"
        CREATE TRIGGER IF NOT EXISTS trig_sessions_updated_at
        AFTER UPDATE ON sessions
        FOR EACH ROW
        BEGIN
            UPDATE sessions SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
        END
        "#
    )
    .execute(pool)
    .await?;

    sqlx::query(
        r#"
        CREATE TRIGGER IF NOT EXISTS trig_users_updated_at
        AFTER UPDATE ON users
        FOR EACH ROW
        BEGIN
            UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
        END
        "#
    )
    .execute(pool)
    .await?;

    sqlx::query(
        r#"
        CREATE TRIGGER IF NOT EXISTS trig_agents_updated_at
        AFTER UPDATE ON agents
        FOR EACH ROW
        BEGIN
            UPDATE agents SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
        END
        "#
    )
    .execute(pool)
    .await?;
    info!("Created triggers");

    // Insert sample data if none exists
    let has_tasks: bool = sqlx::query_scalar(
        "SELECT EXISTS(SELECT 1 FROM tasks)"
    )
        .fetch_one(pool)
        .await?;

    if !has_tasks {
        // Insert a sample project (task with parent_task_id = NULL)
        sqlx::query(
            r#"
            INSERT INTO tasks (id, name, status, description, parent_task_id)
            VALUES ($1, $2, $3, $4, $5)
            "#
        )
        .bind(uuid::Uuid::parse_str("00000000-0000-0000-0000-000000000000")?)
        .bind("Default Project")
        .bind("todo")
        .bind("Default project for tasks")
        .bind(None::<uuid::Uuid>)
        .execute(pool)
        .await?;
        info!("Inserted default project");

        // Insert a sample task
        sqlx::query(
            r#"
            INSERT INTO tasks (id, name, status, description, parent_task_id)
            VALUES ($1, $2, $3, $4, $5)
            "#
        )
        .bind(uuid::Uuid::parse_str("00000000-0000-0000-0000-000000000001")?)
        .bind("Sample Task")
        .bind("todo")
        .bind("This is a sample task")
        .bind(Some(uuid::Uuid::parse_str("00000000-0000-0000-0000-000000000000")?))
        .execute(pool)
        .await?;
        info!("Inserted sample task");
    }

    info!("SQLite database initialization completed successfully");
    Ok(())
}

/// Initialize PostgreSQL database schema
#[cfg(feature = "postgres")]
pub async fn init_postgres_schema(pool: &PgPool) -> Result<()> {
    info!("Initializing PostgreSQL database schema");

    // Create function to update updated_at
    sqlx::query!(
        r#"
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
        "#
    )
    .execute(pool)
    .await?;
    info!("Created update_updated_at_column function");



    // Create enum for task status if it doesn't exist
    let enum_exists = sqlx::query!(
        r#"
        SELECT EXISTS (
            SELECT 1 FROM pg_type WHERE typname = 'task_status'
        ) as "exists!"
        "#
    )
    .fetch_one(pool)
    .await?
    .exists;

    if !enum_exists {
        sqlx::query!(
            r#"
            CREATE TYPE task_status AS ENUM ('todo', 'doing', 'review', 'done')
            "#
        )
        .execute(pool)
        .await?;
        info!("Created task_status enum");
    }

    // Create enum for entity type if it doesn't exist
    let entity_type_exists = sqlx::query!(
        r#"
        SELECT EXISTS (
            SELECT 1 FROM pg_type WHERE typname = 'entity_type'
        ) as "exists!"
        "#
    )
    .fetch_one(pool)
    .await?
    .exists;

    if !entity_type_exists {
        sqlx::query!(
            r#"
            CREATE TYPE entity_type AS ENUM ('user', 'agent')
            "#
        )
        .execute(pool)
        .await?;
        info!("Created entity_type enum");
    }

    // Create enum for resource type if it doesn't exist
    let resource_type_exists = sqlx::query!(
        r#"
        SELECT EXISTS (
            SELECT 1 FROM pg_type WHERE typname = 'resource_type'
        ) as "exists!"
        "#
    )
    .fetch_one(pool)
    .await?
    .exists;

    if !resource_type_exists {
        sqlx::query!(
            r#"
            CREATE TYPE resource_type AS ENUM ('task', 'project', 'user', 'agent', 'system')
            "#
        )
        .execute(pool)
        .await?;
        info!("Created resource_type enum");
    }

    // Create enum for permission level if it doesn't exist
    let permission_level_exists = sqlx::query!(
        r#"
        SELECT EXISTS (
            SELECT 1 FROM pg_type WHERE typname = 'permission_level'
        ) as "exists!"
        "#
    )
    .fetch_one(pool)
    .await?
    .exists;

    if !permission_level_exists {
        sqlx::query!(
            r#"
            CREATE TYPE permission_level AS ENUM ('read', 'write', 'admin')
            "#
        )
        .execute(pool)
        .await?;
        info!("Created permission_level enum");
    }

    // Create tasks table
    sqlx::query!(
        r#"
        CREATE TABLE IF NOT EXISTS tasks (
            id UUID PRIMARY KEY NOT NULL,
            name TEXT NOT NULL,
            status task_status NOT NULL,
            description TEXT,
            tags JSONB NOT NULL DEFAULT '[]',
            assignees JSONB NOT NULL DEFAULT '[]',
            start_time TIMESTAMPTZ,
            end_time TIMESTAMPTZ,
            parent_task_id UUID,
            position INTEGER NOT NULL DEFAULT 0,
            blocking JSONB NOT NULL DEFAULT '[]',
            blocked_by JSONB NOT NULL DEFAULT '[]',
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            FOREIGN KEY (parent_task_id) REFERENCES tasks(id) ON DELETE SET NULL
        )
        "#
    )
    .execute(pool)
    .await?;
    info!("Created tasks table");

    // Create sessions table
    sqlx::query!(
        r#"
        CREATE TABLE IF NOT EXISTS sessions (
            id UUID PRIMARY KEY NOT NULL,
            user_id TEXT NOT NULL,
            token TEXT NOT NULL UNIQUE,
            expires_at TIMESTAMPTZ NOT NULL,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
        "#
    )
    .execute(pool)
    .await?;
    info!("Created sessions table");

    // Create users table
    sqlx::query!(
        r#"
        CREATE TABLE IF NOT EXISTS users (
            id UUID PRIMARY KEY NOT NULL,
            user_id TEXT NOT NULL UNIQUE,
            email TEXT NOT NULL,
            display_name TEXT,
            photo_url TEXT,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            last_login TIMESTAMPTZ
        )
        "#
    )
    .execute(pool)
    .await?;
    info!("Created users table");

    // Create agents table
    sqlx::query!(
        r#"
        CREATE TABLE IF NOT EXISTS agents (
            id UUID PRIMARY KEY NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            parameters JSONB NOT NULL DEFAULT '{}',
            created_by TEXT NOT NULL,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            is_active BOOLEAN NOT NULL DEFAULT TRUE
        )
        "#
    )
    .execute(pool)
    .await?;
    info!("Created agents table");

    // Create permissions table
    sqlx::query!(
        r#"
        CREATE TABLE IF NOT EXISTS permissions (
            id UUID PRIMARY KEY NOT NULL,
            entity_type entity_type NOT NULL,
            entity_id TEXT NOT NULL,
            resource_type resource_type NOT NULL,
            resource_id TEXT,
            permission_level permission_level NOT NULL,
            granted_by TEXT NOT NULL,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
        )
        "#
    )
    .execute(pool)
    .await?;
    info!("Created permissions table");

    // Create indexes
    sqlx::query!("CREATE INDEX IF NOT EXISTS idx_tasks_parent_task_id ON tasks(parent_task_id)")
        .execute(pool)
        .await?;
    sqlx::query!("CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)")
        .execute(pool)
        .await?;
    sqlx::query!("CREATE INDEX IF NOT EXISTS idx_sessions_token ON sessions(token)")
        .execute(pool)
        .await?;
    sqlx::query!("CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id)")
        .execute(pool)
        .await?;
    sqlx::query!("CREATE INDEX IF NOT EXISTS idx_users_user_id ON users(user_id)")
        .execute(pool)
        .await?;
    sqlx::query!("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")
        .execute(pool)
        .await?;
    sqlx::query!("CREATE INDEX IF NOT EXISTS idx_agents_created_by ON agents(created_by)")
        .execute(pool)
        .await?;
    sqlx::query!("CREATE INDEX IF NOT EXISTS idx_agents_is_active ON agents(is_active)")
        .execute(pool)
        .await?;
    sqlx::query!("CREATE INDEX IF NOT EXISTS idx_permissions_entity ON permissions(entity_type, entity_id)")
        .execute(pool)
        .await?;
    sqlx::query!("CREATE INDEX IF NOT EXISTS idx_permissions_resource ON permissions(resource_type, resource_id)")
        .execute(pool)
        .await?;
    info!("Created indexes");

    // Create triggers
    sqlx::query!(
        r#"
        DROP TRIGGER IF EXISTS trig_tasks_updated_at ON tasks;
        CREATE TRIGGER trig_tasks_updated_at
        BEFORE UPDATE ON tasks
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
        "#
    )
    .execute(pool)
    .await?;

    sqlx::query!(
        r#"
        DROP TRIGGER IF EXISTS trig_sessions_updated_at ON sessions;
        CREATE TRIGGER trig_sessions_updated_at
        BEFORE UPDATE ON sessions
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
        "#
    )
    .execute(pool)
    .await?;

    sqlx::query!(
        r#"
        DROP TRIGGER IF EXISTS trig_users_updated_at ON users;
        CREATE TRIGGER trig_users_updated_at
        BEFORE UPDATE ON users
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
        "#
    )
    .execute(pool)
    .await?;

    sqlx::query!(
        r#"
        DROP TRIGGER IF EXISTS trig_agents_updated_at ON agents;
        CREATE TRIGGER trig_agents_updated_at
        BEFORE UPDATE ON agents
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
        "#
    )
    .execute(pool)
    .await?;
    info!("Created triggers");

    // Insert sample data if none exists
    let count = sqlx::query!("SELECT COUNT(*) as count FROM tasks")
        .fetch_one(pool)
        .await?
        .count;

    if count == 0 {
        // Insert a sample project (task with parent_task_id = NULL)
        sqlx::query!(
            r#"
            INSERT INTO tasks (id, name, status, description, parent_task_id)
            VALUES ($1, $2, $3, $4, $5)
            "#,
            uuid::Uuid::parse_str("00000000-0000-0000-0000-000000000000")?,
            "Default Project",
            "todo",
            "Default project for tasks",
            None::<uuid::Uuid>
        )
        .execute(pool)
        .await?;
        info!("Inserted default project");

        // Insert a sample task
        sqlx::query!(
            r#"
            INSERT INTO tasks (id, name, status, description, parent_task_id)
            VALUES ($1, $2, $3, $4, $5)
            "#,
            uuid::Uuid::parse_str("00000000-0000-0000-0000-000000000001")?,
            "Sample Task",
            "todo",
            "This is a sample task",
            Some(uuid::Uuid::parse_str("00000000-0000-0000-0000-000000000000")?)
        )
        .execute(pool)
        .await?;
        info!("Inserted sample task");
    }

    info!("PostgreSQL database initialization completed successfully");
    Ok(())
}
