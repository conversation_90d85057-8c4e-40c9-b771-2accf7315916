{"rustc": 10895048813736897673, "features": "[\"_rt-tokio\", \"_tls-rustls-ring-webpki\", \"chrono\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"sqlite\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlite-unbundled\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 1384594146405796144, "path": 15067526977068106227, "deps": [[8986759836770526006, "syn", false, 13717353883845142581], [9343754438965136657, "sqlx_macros_core", false, 6471981126810173664], [12410540580958238005, "proc_macro2", false, 10639316160010260521], [16423736323724667376, "sqlx_core", false, 7790652359597707370], [17990358020177143287, "quote", false, 14279578803395008540]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-fe8ec774fe4d195f\\dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}