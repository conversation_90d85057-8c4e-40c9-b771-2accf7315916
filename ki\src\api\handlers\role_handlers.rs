use axum::{
    extract::{Path, State},
    http::StatusCode,
    Json,
};
use uuid::Uuid;
use crate::{
    db::{
        models::{
            NewRole, UpdateRole, RoleResponse, RoleDetails, RoleTreeNode,
            SetRoleParentRequest, BulkRelationshipOperation, PermissionCheckRequest,
            PermissionCheckResponse,
        },
        repositories::role_repository::RoleRepository,
    },

    ServerState,
};

/// Create a new role
pub async fn create_role(
    State(server_state): State<ServerState>,
    Json(new_role): Json<NewRole>,
) -> Result<(StatusCode, Json<RoleResponse>), (StatusCode, String)> {
    let repo = RoleRepository::new(server_state.db.clone());

    // Check if role name already exists
    match repo.exists_by_name(&new_role.name).await {
        Ok(true) => return Err((StatusCode::CONFLICT, "Role name already exists".to_string())),
        Ok(false) => {},
        Err(e) => return Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }

    match repo.create(new_role).await {
        Ok(role) => Ok((StatusCode::CREATED, Json(role.into()))),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get all roles
pub async fn get_roles(
    State(server_state): State<ServerState>,
) -> Result<(StatusCode, Json<Vec<RoleResponse>>), (StatusCode, String)> {
    let repo = RoleRepository::new(server_state.db.clone());

    match repo.get_all().await {
        Ok(roles) => {
            let role_responses: Vec<RoleResponse> = roles.into_iter().map(|role| role.into()).collect();
            Ok((StatusCode::OK, Json(role_responses)))
        }
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get role by ID
pub async fn get_role(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<(StatusCode, Json<RoleResponse>), (StatusCode, String)> {
    let role_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => return Err((StatusCode::BAD_REQUEST, "Invalid role ID format".to_string())),
    };

    let repo = RoleRepository::new(server_state.db.clone());

    match repo.get_by_id(role_id).await {
        Ok(Some(role)) => Ok((StatusCode::OK, Json(role.into()))),
        Ok(None) => Err((StatusCode::NOT_FOUND, "Role not found".to_string())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Update role
pub async fn update_role(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
    Json(update_role): Json<UpdateRole>,
) -> Result<(StatusCode, Json<RoleResponse>), (StatusCode, String)> {
    let role_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => return Err((StatusCode::BAD_REQUEST, "Invalid role ID format".to_string())),
    };

    let repo = RoleRepository::new(server_state.db.clone());

    // Check if new name already exists (if name is being updated)
    if let Some(ref name) = update_role.name {
        match repo.exists_by_name(name).await {
            Ok(true) => {
                // Check if it's the same role
                match repo.get_by_id(role_id).await {
                    Ok(Some(existing_role)) => {
                        if existing_role.name != *name {
                            return Err((StatusCode::CONFLICT, "Role name already exists".to_string()));
                        }
                    }
                    Ok(None) => return Err((StatusCode::NOT_FOUND, "Role not found".to_string())),
                    Err(e) => return Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
                }
            }
            Ok(false) => {},
            Err(e) => return Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
        }
    }

    match repo.update(role_id, update_role).await {
        Ok(Some(role)) => Ok((StatusCode::OK, Json(role.into()))),
        Ok(None) => Err((StatusCode::NOT_FOUND, "Role not found".to_string())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Delete role
pub async fn delete_role(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<StatusCode, (StatusCode, String)> {
    let role_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => return Err((StatusCode::BAD_REQUEST, "Invalid role ID format".to_string())),
    };

    let repo = RoleRepository::new(server_state.db.clone());

    match repo.delete(role_id).await {
        Ok(true) => Ok(StatusCode::NO_CONTENT),
        Ok(false) => Err((StatusCode::NOT_FOUND, "Role not found".to_string())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get role tree (hierarchy)
pub async fn get_role_tree(
    State(_server_state): State<ServerState>,
) -> Result<(StatusCode, Json<Vec<RoleTreeNode>>), (StatusCode, String)> {
    // TODO: Implement SpiceDB integration to build role tree
    tracing::info!("get_role_tree called - placeholder implementation");
    Ok((StatusCode::OK, Json(vec![])))
}

/// Get role details with members
pub async fn get_role_details(
    State(_server_state): State<ServerState>,
    Path(_id): Path<String>,
) -> Result<(StatusCode, Json<RoleDetails>), (StatusCode, String)> {
    // TODO: Implement SpiceDB integration to get role details with members
    tracing::info!("get_role_details called - placeholder implementation");
    Err((StatusCode::NOT_IMPLEMENTED, "Not yet implemented".to_string()))
}

/// Set role parent
pub async fn set_role_parent(
    State(_server_state): State<ServerState>,
    Path(_id): Path<String>,
    Json(_request): Json<SetRoleParentRequest>,
) -> Result<StatusCode, (StatusCode, String)> {
    // TODO: Implement SpiceDB integration for atomic role parent setting
    tracing::info!("set_role_parent called - placeholder implementation");
    Ok(StatusCode::OK)
}

/// Bulk relationship operations
pub async fn bulk_relationships(
    State(_server_state): State<ServerState>,
    Json(_operations): Json<Vec<BulkRelationshipOperation>>,
) -> Result<StatusCode, (StatusCode, String)> {
    // TODO: Implement SpiceDB bulk operations
    tracing::info!("bulk_relationships called - placeholder implementation");
    Ok(StatusCode::OK)
}

/// Check permissions
pub async fn check_permissions(
    State(_server_state): State<ServerState>,
    Json(_request): Json<PermissionCheckRequest>,
) -> Result<(StatusCode, Json<PermissionCheckResponse>), (StatusCode, String)> {
    // TODO: Implement SpiceDB permission checking
    tracing::info!("check_permissions called - placeholder implementation");
    let response = PermissionCheckResponse {
        results: vec![],
    };
    Ok((StatusCode::OK, Json(response)))
}
