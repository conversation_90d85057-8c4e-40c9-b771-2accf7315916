{"rustc": 10895048813736897673, "features": "[\"cors\", \"default\", \"trace\", \"tracing\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 2241668132362809309, "path": 5132202876701392762, "deps": [[784494742817713399, "tower_service", false, 5023113295579284643], [1906322745568073236, "pin_project_lite", false, 13483621581492765273], [6166349630582887940, "bitflags", false, 1069913959554015464], [7712452662827335977, "tower_layer", false, 14302084456410726127], [8606274917505247608, "tracing", false, 1371896557796439004], [9010263965687315507, "http", false, 13780035913158987009], [14084095096285906100, "http_body", false, 13164054679762100366], [16066129441945555748, "bytes", false, 14214947457689394275]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-31297a09c5747220\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}