mod api;
mod db;
mod services;

use axum::http::Method;
use rs_firebase_admin_sdk::{
    auth::token::{
        cache::HttpCache, crypto::JwtRsaPubKey, LiveTokenVerifier
    }, credentials_provider, App
};
use std::{collections::BTreeMap, sync::Arc};
use tower_http::cors::CorsLayer;
use tower_http::trace::TraceLayer;

type AppVerifier = LiveTokenVerifier<HttpCache<reqwest::Client, BTreeMap<String, JwtRsaPubKey>>>;

#[derive(Clone)]
pub struct ServerState {
    pub db: db::Database,
    pub verifier: Arc<AppVerifier>,
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // Load .env file if it exists
    let _ = dotenv::dotenv();

    // Initialize tracing
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::DEBUG)
        .init();

    // Initialize database
    let db = db::Database::new().await?;
    tracing::info!("Database connected");

    let verifier = Arc::new(
        App::live(credentials_provider().await?)
            .await.expect("cannot receive google live app")
            .id_token_verifier()
            .await.expect("cannot receive google live token verifier")
    );

    // Configure CORS with specific origin for credentials
    let frontend_url = std::env::var("FRONTEND_URL")
        .unwrap_or_else(|_| "http://localhost:5000".to_string());

    // When using credentials, we need to specify exact headers and origins
    let cors = CorsLayer::new()
        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE])
        .allow_headers([
            axum::http::header::AUTHORIZATION,
            axum::http::header::CONTENT_TYPE,
            axum::http::header::ACCEPT,
        ])
        .allow_origin(frontend_url.parse::<axum::http::HeaderValue>().unwrap())
        .allow_credentials(true);

    // Build our application with routes
    let app = api::create_router(ServerState { db, verifier })
        .layer(TraceLayer::new_for_http())
        .layer(cors);

    // Get port from environment or use default
    let port = std::env::var("PORT")
        .unwrap_or_else(|_| "3000".to_string())
        .parse::<u16>()
        .unwrap_or(3000);

    let addr = std::net::SocketAddr::from(([0, 0, 0, 0], port));
    tracing::info!("Listening on {}", addr);

    // Run our app with hyper
    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}