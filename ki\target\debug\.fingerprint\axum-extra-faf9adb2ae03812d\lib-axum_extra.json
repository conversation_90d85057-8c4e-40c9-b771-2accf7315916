{"rustc": 10895048813736897673, "features": "[\"cookie\", \"default\", \"tracing\"]", "declared_features": "[\"__private_docs\", \"async-read-body\", \"async-stream\", \"attachment\", \"cookie\", \"cookie-key-expansion\", \"cookie-private\", \"cookie-signed\", \"default\", \"erased-json\", \"error-response\", \"file-stream\", \"form\", \"json-deserializer\", \"json-lines\", \"multipart\", \"protobuf\", \"query\", \"scheme\", \"tracing\", \"typed-header\", \"typed-routing\"]", "target": 4770478002602207591, "profile": 13877304307972683652, "path": 4569437634808999596, "deps": [[784494742817713399, "tower_service", false, 5023113295579284643], [1906322745568073236, "pin_project_lite", false, 13483621581492765273], [1999399154011168049, "rustversion", false, 12095854159049431513], [5695049318159433696, "tower", false, 2238454123613380397], [7712452662827335977, "tower_layer", false, 14302084456410726127], [9010263965687315507, "http", false, 13780035913158987009], [9689903380558560274, "serde", false, 9156359934049382691], [10229185211513642314, "mime", false, 15775420741624474642], [10629569228670356391, "futures_util", false, 12506543492908000869], [14084095096285906100, "http_body", false, 13164054679762100366], [15176407853393882315, "axum_core", false, 3055523523914733662], [16066129441945555748, "bytes", false, 14214947457689394275], [16727543399706004146, "cookie", false, 4563071368651451978], [16900715236047033623, "http_body_util", false, 11044969495969053557], [17809372758784730012, "axum", false, 5274925255075762546]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-extra-faf9adb2ae03812d\\dep-lib-axum_extra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}