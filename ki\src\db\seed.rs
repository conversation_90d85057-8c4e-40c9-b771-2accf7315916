use anyhow::Result;
use uuid::Uuid;
use crate::db::{Database, DatabaseConnection, models::NewRole};

/// Seed default roles if they don't exist
pub async fn seed_default_roles(db: &Database) -> Result<()> {
    tracing::info!("Checking for default roles...");

    // Check if any roles exist
    let has_roles = match &db.connection as &DatabaseConnection {
        #[cfg(feature = "sqlite")]
        DatabaseConnection::Sqlite(pool) => {
            let count: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM roles")
                .fetch_one(pool)
                .await?;
            count > 0
        }
        #[cfg(feature = "postgres")]
        DatabaseConnection::Postgres(pool) => {
            let count = sqlx::query!("SELECT COUNT(*) as count FROM roles")
                .fetch_one(pool)
                .await?
                .count
                .unwrap_or(0);
            count > 0
        }
        #[allow(unreachable_patterns)]
        _ => return Err(anyhow::anyhow!("Unsupported database connection type")),
    };

    if has_roles {
        tracing::info!("Roles already exist, skipping seeding");
        return Ok(());
    }

    tracing::info!("No roles found, creating default roles...");

    // Create default roles
    let admin_role = NewRole {
        name: "Admin".to_string(),
        description: Some("Full administrative access to the system".to_string()),
        color: Some("#dc2626".to_string()), // Red
    };

    let member_role = NewRole {
        name: "Member".to_string(),
        description: Some("Standard member access".to_string()),
        color: Some("#2563eb".to_string()), // Blue
    };

    // Insert roles in a transaction
    match &db.connection as &DatabaseConnection {
        #[cfg(feature = "sqlite")]
        DatabaseConnection::Sqlite(pool) => {
            let mut tx = pool.begin().await?;

            // Insert Admin role
            let admin_id = Uuid::new_v4();
            sqlx::query(
                r#"
                INSERT INTO roles (id, name, description, color)
                VALUES (?, ?, ?, ?)
                "#
            )
            .bind(admin_id)
            .bind(&admin_role.name)
            .bind(&admin_role.description)
            .bind(&admin_role.color)
            .execute(&mut *tx)
            .await?;

            // Insert Member role
            let member_id = Uuid::new_v4();
            sqlx::query(
                r#"
                INSERT INTO roles (id, name, description, color)
                VALUES (?, ?, ?, ?)
                "#
            )
            .bind(member_id)
            .bind(&member_role.name)
            .bind(&member_role.description)
            .bind(&member_role.color)
            .execute(&mut *tx)
            .await?;

            tx.commit().await?;

            tracing::info!("Created default roles: Admin ({}), Member ({})", admin_id, member_id);
        }
        #[cfg(feature = "postgres")]
        DatabaseConnection::Postgres(pool) => {
            let mut tx = pool.begin().await?;

            // Insert Admin role
            let admin_id = Uuid::new_v4();
            sqlx::query!(
                r#"
                INSERT INTO roles (id, name, description, color)
                VALUES ($1, $2, $3, $4)
                "#,
                admin_id,
                admin_role.name,
                admin_role.description,
                admin_role.color
            )
            .execute(&mut *tx)
            .await?;

            // Insert Member role
            let member_id = Uuid::new_v4();
            sqlx::query!(
                r#"
                INSERT INTO roles (id, name, description, color)
                VALUES ($1, $2, $3, $4)
                "#,
                member_id,
                member_role.name,
                member_role.description,
                member_role.color
            )
            .execute(&mut *tx)
            .await?;

            tx.commit().await?;

            tracing::info!("Created default roles: Admin ({}), Member ({})", admin_id, member_id);
        }
        #[allow(unreachable_patterns)]
        _ => return Err(anyhow::anyhow!("Unsupported database connection type")),
    }

    // TODO: Create corresponding SpiceDB relationships when SpiceDB service is integrated
    tracing::info!("Default roles seeded successfully");
    Ok(())
}
