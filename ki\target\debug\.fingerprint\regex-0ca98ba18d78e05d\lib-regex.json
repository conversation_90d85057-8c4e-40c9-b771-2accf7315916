{"rustc": 10895048813736897673, "features": "[\"std\", \"unicode-bool\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2241668132362809309, "path": 17441511570889624816, "deps": [[555019317135488525, "regex_automata", false, 487738571842265490], [9408802513701742484, "regex_syntax", false, 5991740648143646527]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-0ca98ba18d78e05d\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}