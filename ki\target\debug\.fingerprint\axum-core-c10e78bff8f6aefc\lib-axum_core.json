{"rustc": 10895048813736897673, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 13877304307972683652, "path": 13469109972925946515, "deps": [[784494742817713399, "tower_service", false, 5023113295579284643], [1906322745568073236, "pin_project_lite", false, 13483621581492765273], [1999399154011168049, "rustversion", false, 12095854159049431513], [2517136641825875337, "sync_wrapper", false, 17561740311626693908], [7620660491849607393, "futures_core", false, 14165407329339629864], [7712452662827335977, "tower_layer", false, 14302084456410726127], [8606274917505247608, "tracing", false, 1371896557796439004], [9010263965687315507, "http", false, 13780035913158987009], [10229185211513642314, "mime", false, 15775420741624474642], [14084095096285906100, "http_body", false, 13164054679762100366], [16066129441945555748, "bytes", false, 14214947457689394275], [16900715236047033623, "http_body_util", false, 11044969495969053557]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-core-c10e78bff8f6aefc\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}