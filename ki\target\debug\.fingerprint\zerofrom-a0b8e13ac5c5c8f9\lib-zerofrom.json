{"rustc": 10895048813736897673, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 2241668132362809309, "path": 16798977816863026250, "deps": [[4022439902832367970, "zerofrom_derive", false, 8249009870697984639]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-a0b8e13ac5c5c8f9\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}