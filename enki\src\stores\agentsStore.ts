import { defineStore } from "pinia";
import { ref } from "vue";
import type { Agent } from "@/types";
import { getAllAgents, createAgent, updateAgent, deleteAgent } from "@/services/kiApi";
import { useSpacesStore } from "./spacesStore";

export const useAgentsStore = defineStore('agents', {
    state: () => ({
        agents: ref<Agent[]>([]),
        loading: ref(false),
        error: ref<string | null>(null)
    }),
    getters: {
        getAgent: (state) => (id: string) => {
            return state.agents.find(agent => agent.id === id);
        },
        getActiveAgents: (state) => {
            return state.agents.filter(agent => agent.is_active);
        },
        getAgentsByCreator: (state) => (createdBy: string) => {
            return state.agents.filter(agent => agent.created_by === createdBy);
        }
    },
    actions: {
        async fetchAgents(activeOnly: boolean = false) {
            this.loading = true;
            this.error = null;
            try {
                const spacesStore = useSpacesStore();
                const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
                if (!server) {
                    throw new Error('No server configured');
                }
                
                this.agents = await getAllAgents(server, activeOnly);
            } catch (error) {
                this.error = error instanceof Error ? error.message : 'Failed to fetch agents';
                console.error('Error fetching agents:', error);
            } finally {
                this.loading = false;
            }
        },

        async createAgent(agentData: Omit<Agent, 'id' | 'created_at' | 'updated_at'>) {
            this.loading = true;
            this.error = null;
            try {
                const spacesStore = useSpacesStore();
                const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
                if (!server) {
                    throw new Error('No server configured');
                }

                const newAgent = await createAgent(server, agentData);
                this.agents.push(newAgent);
                return newAgent;
            } catch (error) {
                this.error = error instanceof Error ? error.message : 'Failed to create agent';
                console.error('Error creating agent:', error);
                throw error;
            } finally {
                this.loading = false;
            }
        },

        async updateAgent(id: string, agentData: Partial<Agent>) {
            this.loading = true;
            this.error = null;
            try {
                const spacesStore = useSpacesStore();
                const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
                if (!server) {
                    throw new Error('No server configured');
                }

                const updatedAgent = await updateAgent(server, { id, ...agentData } as Agent);
                const index = this.agents.findIndex(agent => agent.id === id);
                if (index !== -1) {
                    this.agents[index] = updatedAgent;
                }
                return updatedAgent;
            } catch (error) {
                this.error = error instanceof Error ? error.message : 'Failed to update agent';
                console.error('Error updating agent:', error);
                throw error;
            } finally {
                this.loading = false;
            }
        },

        async deleteAgent(id: string) {
            this.loading = true;
            this.error = null;
            try {
                const spacesStore = useSpacesStore();
                const server = spacesStore.spaces[spacesStore.currentSpaceId]?.server;
                if (!server) {
                    throw new Error('No server configured');
                }

                await deleteAgent(server, id);
                const index = this.agents.findIndex(agent => agent.id === id);
                if (index !== -1) {
                    this.agents.splice(index, 1);
                }
            } catch (error) {
                this.error = error instanceof Error ? error.message : 'Failed to delete agent';
                console.error('Error deleting agent:', error);
                throw error;
            } finally {
                this.loading = false;
            }
        }
    }
});
