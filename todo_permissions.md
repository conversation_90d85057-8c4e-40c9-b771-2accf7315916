Of course. Here is a comprehensive, production-ready implementation plan, written as a complete and final specification.

***

# Roles & Permissions System: A SpiceDB Implementation Plan

## 1. Executive Summary

This document outlines a definitive plan to implement a robust, Discord-like roles and permissions system using SpiceDB. The foundational principle is that **SpiceDB will be the single source of truth for all authorization data**. The application's database will only store non-authorization metadata (e.g., role names, colors).

This plan is engineered for production readiness, incorporating best practices for schema design, data consistency, security, and operational stability. Key features of this design include a compiler-correct schema that handles permission inheritance, atomic mechanisms to prevent data corruption from race conditions, and a full suite of security and monitoring protocols.

All backend communication with SpiceDB will be handled via the `spicedb-rust` crate.

## 2. System Architecture

### 2.1 SpiceDB Schema
The schema is designed for correctness, clarity, and to intuitively handle permission inheritance. It enforces a tree structure for roles and uses type-specific relations to remain syntactically valid.

```spicedb
definition user {}

definition role {
    relation member: user
    // A role can have at most one parent to enforce a tree structure,
    // which simplifies cycle prevention.
    relation parent: role 
    
    // This permission correctly computes transitive membership.
    // A user is a member of this role if they are a direct member
    // OR a member of any parent role, recursively.
    permission all_members = member + parent->all_members
}

definition task {
    // Relations are split by type for schema correctness. A permission
    // cannot be a subject type, so we relate to roles directly.
    relation owner_user: user
    relation owner_role: role
    relation assignee: user
    relation viewer_user: user
    relation viewer_role: role
    relation editor_user: user
    relation editor_role: role
    relation admin_user: user
    relation admin_role: role

    // Permissions use arrow notation to traverse relations to roles
    // and check against their computed 'all_members' permission.
    permission owner = owner_user + owner_role->all_members
    permission viewer = viewer_user + viewer_role->all_members
    permission editor = editor_user + editor_role->all_members
    permission admin = admin_user + admin_role->all_members

    permission view = viewer + editor + admin + owner + assignee
    permission edit = editor + admin + owner
    permission delete = admin + owner
}

definition global_settings {
    relation owner_user: user
    relation owner_role: role
    relation admin_user: user
    relation admin_role: role

    permission owner = owner_user + owner_role->all_members
    permission admin = admin_user + admin_role->all_members

    permission manage_roles = admin + owner
    permission invite_users = admin + owner
}
```

### 2.2 API Endpoint Strategy
The API abstracts all SpiceDB complexity and provides the frontend with clean, ready-to-use data, including support for large-scale roles.

| Data Type | API Endpoint Examples | System of Record | Description |
| :--- | :--- | :--- | :--- |
| **Role Metadata** | `POST /api/roles` <br/> `PUT /api/roles/{id}` | SQL Database | Manages non-authorization data like name and color. |
| **Role Graph Data** | `GET /api/roles/tree` <br/> `GET /api/roles/{id}/details` | SpiceDB | Read-only endpoints for UI to display hierarchy and member lists. **Supports pagination via cursors.** |
| **Role Hierarchy** | `PUT /api/roles/{id}/parent` | SpiceDB | **Atomic:** Uses a distributed lock and a precondition for a true compare-and-set operation. |
| **Bulk Operations** | `POST /api/transactions/relationships` | SpiceDB | Atomically creates and/or deletes multiple relationships in a single call. |
| **Permission Checks** | `POST /api/permissions/check` | SpiceDB | Verifies permissions, using `BatchCheckPermission` where possible to reduce latency. |

## 3. Core Implementation Patterns

### 3.1 Secure ZedToken Management
To ensure read-after-write consistency without leaking internal data, `ZedTokens` will be managed by a dedicated middleware.

*   **Guaranteed Cleanup**: The middleware will ensure the `ZedToken` is cleared from the request context upon completion, even in the case of panics or 5xx errors. This will be implemented using language features like Rust's `Drop` trait for robust cleanup.
*   **Boundary Control**: This middleware will also strip the `ZedToken` from any outgoing request headers to external, untrusted services (e.g., payment gateways, webhooks), preventing the exposure of internal database revision IDs.

## 4. Security, Performance, and Monitoring

### 4.1 Security: mTLS as Standard
Communication between the backend and SpiceDB will be secured using **mutual TLS (mTLS)**. The use of `--grpc-preshared-key` is disabled. This provides strong, per-service identity verification and allows for automated certificate rotation using a service mesh like Istio or Linkerd.

### 4.2 Performance: Batching and Pagination
*   **UI Rendering**: To minimize latency when rendering UIs with multiple permission-gated elements, the backend will use SpiceDB's `BatchCheckPermission` RPC.
*   **Member Counts**: To provide accurate member counts for roles of any size, the `GET /api/roles/{id}/details` endpoint will use `LookupSubjects` and fully support cursor-based pagination.

### 4.3 Monitoring: Correct and Actionable Metrics
Dashboards and alerts will be configured to monitor the health and performance of the authorization system using the following key metrics from SpiceDB:
*   `spicedb_dispatch_duration_seconds` (histogram): To monitor query latency and alert on p95/p99 spikes.
*   `spicedb_dispatch_depth_total` (counter): To track the depth of permission checks and provide an early warning for overly complex role hierarchies.
*   **gRPC Status Codes**: High-priority alerts will be configured for `FAILED_PRECONDITION` responses, which signal a potential recursion depth issue.

## 5. Data Initialization and Testing

### 5.1 Idempotent & Transactional Initialization
The initial seeding script that creates default roles will be both idempotent and transactional to prevent orphaned data. The logic will first start an SQL transaction, write the metadata to the application database, then write the relationships to SpiceDB. The SQL transaction is only committed if the SpiceDB write succeeds; otherwise, it is rolled back.

### 5.2 CI/CD: Compatibility Testing
The continuous integration pipeline will include a **compatibility matrix test**. This test will spin up a SpiceDB container and run the full application test suite against it. The SpiceDB container image tag (e.g., `v1.30.0`) will be explicitly matched with the `spicedb-rust` client library's known wire compatibility level to prevent subtle bugs caused by version mismatches. End-to-end tests will specifically attempt to trigger race conditions on hierarchy writes to validate the locking mechanism.

## 6. Phased Implementation Plan & Testing

This plan breaks down the project into four distinct phases, from foundational backend development to full system rollout. Testing is integrated into each phase to catch issues early and validate correctness at every stage.

### Phase 1: Foundation & Backend Development

**Goal**: To build a secure, robust, and fully functional backend service that correctly implements the core authorization logic according to the defined architecture.

**Steps**:

1.  **Infrastructure Setup**:
    *   Deploy SpiceDB via Docker.
    *   Configure the private Certificate Authority (CA) to issue and manage certificates for **mutual TLS (mTLS)** between the backend service and SpiceDB.
    *   Add the `spicedb-rust` dependency.
    *   Set up the database schema for storing role metadata (`roles` table).

2.  **Schema Deployment**:
    *   Finalize the SpiceDB schema and save it to a `.zed` file within the repository.
    *   Create a script (`zed schema write --endpoint <...>` or similar) to be integrated into the CI/CD pipeline, ensuring the schema is applied automatically during deployment.

3.  **Core Service Implementation (`SpiceDBService`)**:
    *   Implement the SpiceDB client connection logic, ensuring it uses the mTLS credentials.
    *   Develop the critical middleware for **ZedToken aggregation and sanitization**. This middleware will track the latest token within a request's lifecycle and strip it before any external API calls.

4.  **API Endpoint Development**:
    *   Build all API endpoints as defined in the architectural plan, including:
        *   Metadata endpoints (`POST /api/roles`).
        *   The atomic hierarchy endpoint (`PUT /api/roles/{id}/parent`).
        *   Bulk transaction endpoint (`POST /api/transactions/relationships`).
        *   Optimized check endpoint (`POST /api/permissions/check`) using `BatchCheckPermission`.
        *   Data endpoints for the UI (`GET /api/roles/tree`, `GET /api/roles/{id}/details`) with full support for cursor-based pagination.

5.  **Data Initialization**:
    *   Write the **idempotent and transactional seeding script** that creates the default "Admin" and "Member" roles. This script will run on application startup only if default roles are not found, and it will guarantee atomicity between the SQL and SpiceDB writes.

### Phase 2: Backend & Integration Testing

**Goal**: To rigorously validate that the backend is functionally correct, secure, and performs as expected before any UI development relies on it.

**Testing Strategy**:

*   **Unit Tests**:
    *   **ZedToken Middleware**: Write specific tests to force panics and 5xx errors to verify the middleware correctly cleans up the token context and prevents leaks. Test the header-stripping logic for external calls.
    *   **Helper Functions**: Test all utility functions for building SpiceDB requests and handling responses in isolation.

*   **Integration Tests (CI/CD Pipeline)**:
    *   **Compatibility Matrix Test**: This is a critical CI job. It will run the entire integration test suite against a matrix of compatible `spicedb-rust` crate versions and `quay.io/authzed/spicedb:v1.x.y` container image tags to catch any wire-format or behavioral incompatibilities.
    *   **Cycle Prevention Test**:
        *   Spawn multiple concurrent threads that attempt to create a role hierarchy cycle (e.g., A → B and B → A). The test must assert that the locking mechanism correctly serializes the requests and that one write fails, preventing a cycle.
        *   Assert that attempting to assign a second parent to a role is rejected due to the precondition.
    *   **Consistency Test**: Perform a write followed immediately by a read within the same test and assert the read reflects the write, validating the ZedToken propagation logic.
    *   **API Endpoint Tests**: Write tests for every API endpoint, validating success cases, failure cases (e.g., bad input), and authentication/authorization rules.
    *   **Seeding Script Test**: Verify the initialization script is idempotent (can be run multiple times without changing the final state or erroring) and transactional (correctly rolls back the SQL transaction if the SpiceDB write fails).

### Phase 3: Frontend Implementation

**Goal**: To build a responsive and intuitive user interface that correctly consumes the backend APIs and provides a seamless experience for managing roles and permissions.

**Steps**:

1.  **Store & State Management**:
    *   Set up state management stores (e.g., Pinia) for roles, permissions, and user data. These stores will encapsulate all API communication.

2.  **Global Roles Management UI**:
    *   Develop the primary Roles administration page (`/admin/roles`).
    *   Build UI components for creating/editing role metadata, managing the role hierarchy (e.g., via drag-and-drop), and managing role members.
    *   Integrate API calls to the paginated `details` and `tree` endpoints to correctly display member counts and the hierarchy visual.

3.  **Resource-Specific & Conditional UI**:
    *   Integrate permission controls directly into resource views (e.g., a "Permissions" panel on a Task page).
    *   Implement conditional rendering throughout the application. UI elements like buttons and navigation links will be enabled, disabled, or hidden based on the results of permission checks from the backend.

### Phase 4: Full System Testing & Rollout

**Goal**: To validate the entire system end-to-end, from user interaction to database state, and execute a safe, monitored rollout to production.

**Testing Strategy**:

*   **End-to-End (E2E) Tests**:
    *   Using a framework like Cypress or Playwright, script full user workflows that mimic real-world scenarios, such as:
        1.  An admin creates a new role and assigns it permissions.
        2.  A new user signs up and inherits permissions from a default role.
        3.  The user correctly accesses a resource based on inherited permissions and is correctly denied access to another.
        4.  The admin revokes a permission, and the user's access is immediately revoked.

*   **Performance & Load Testing**:
    *   Use a tool like k6 or JMeter to simulate high-traffic scenarios against a staging environment.
    *   Focus on the read-heavy `check` endpoints and write-heavy hierarchy modification endpoints to identify bottlenecks.

*   **Security Testing**:
    *   Conduct a final security review, ensuring mTLS is enforced and all API endpoints have appropriate authorization checks.

**Rollout Strategy**:

1.  **Staging Deployment & Monitoring Validation**:
    *   Deploy the full application to a production-like staging environment.
    *   Connect monitoring tools (Prometheus, Grafana) and verify that dashboards populate with the correct metrics (`spicedb_dispatch_duration_seconds`, etc.). Intentionally trigger errors or deep hierarchies to ensure alerts fire as expected.

2.  **Canary Release**:
    *   Deploy the new system to production behind a feature flag.
    *   Initially, enable the feature for a small, controlled group of users (e.g., internal staff).

3.  **Full Rollout**:
    *   Closely monitor error rates, API latency, and the key SpiceDB performance metrics.
    *   If the system remains stable and performant, gradually roll out the feature to all users over a period of several days.