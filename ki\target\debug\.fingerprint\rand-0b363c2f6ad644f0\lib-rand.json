{"rustc": 10895048813736897673, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"small_rng\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2241668132362809309, "path": 15661519733748217796, "deps": [[1573238666360410412, "rand_chacha", false, 16681500277517255650], [18130209639506977569, "rand_core", false, 591200597264876695]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-0b363c2f6ad644f0\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}