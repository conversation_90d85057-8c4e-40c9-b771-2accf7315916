# Phase 1: SpiceDB Roles & Permissions System Implementation

This document outlines the completed Phase 1 implementation of the SpiceDB-based roles and permissions system for the Ki server.

## Overview

Phase 1 establishes the foundation for a robust, production-ready authorization system using SpiceDB as the single source of truth for all authorization relationships, with the SQL database storing only metadata.

## Completed Components

### 1. Infrastructure Setup ✅

#### SpiceDB Integration
- **Docker Compose Configuration**: Added SpiceDB container to `docker-compose.yml`
  - SpiceDB v1.30.0 with memory datastore for development
  - Exposed ports: 8080 (HTTP), 9090 (metrics), 50051 (gRPC)
  - Pre-shared key authentication for development

#### Dependencies
- **Added to Cargo.toml**:
  - `spicedb-rust = "0.1.0"` - SpiceDB Rust client
  - `tonic = "0.12.3"` - gRPC client
  - `tonic-build = "0.12.3"` - gRPC build tools

#### Database Schema
- **Roles Table**: Added to both SQLite and PostgreSQL schemas
  ```sql
  CREATE TABLE roles (
      id UUID PRIMARY KEY,
      name TEXT NOT NULL UNIQUE,
      description TEXT,
      color TEXT,
      created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
  );
  ```

### 2. Schema Deployment ✅

#### SpiceDB Schema
- **File**: `schema.zed`
- **Definitions**:
  - `user` - Basic user entity
  - `role` - Hierarchical roles with parent relationships
  - `task` - Task permissions with role-based access
  - `global_settings` - System-wide permissions

#### Key Features
- **Tree Structure**: Roles enforce single-parent hierarchy to prevent cycles
- **Permission Inheritance**: Users inherit permissions from parent roles
- **Type Safety**: Relations split by type for schema correctness

#### Deployment Script
- **File**: `deploy-schema.sh`
- **Features**:
  - Automated schema deployment using `zed` CLI
  - Schema validation
  - Environment variable configuration

### 3. Core Service Implementation ✅

#### SpiceDBService
- **File**: `src/services/spicedb_service.rs`
- **Features**:
  - mTLS connection support (placeholder for production)
  - Insecure connection for development
  - ZedToken management for read-after-write consistency
  - Core operations: role membership, parent setting, permission checking

#### ZedToken Middleware
- **File**: `src/api/middleware/zed_token.rs`
- **Features**:
  - Request-scoped token management
  - Automatic cleanup on request completion
  - Security: strips tokens from external responses
  - Panic-safe with proper Drop semantics

### 4. API Endpoint Development ✅

#### Role Metadata Endpoints
- `POST /api/roles` - Create role
- `GET /api/roles` - List all roles
- `GET /api/roles/{id}` - Get role by ID
- `PUT /api/roles/{id}` - Update role
- `DELETE /api/roles/{id}` - Delete role

#### SpiceDB Integration Endpoints (Placeholder)
- `GET /api/roles/tree` - Get role hierarchy
- `GET /api/roles/{id}/details` - Get role with members
- `PUT /api/roles/{id}/parent` - Set role parent (atomic)
- `POST /api/transactions/relationships` - Bulk operations
- `POST /api/permissions/check` - Batch permission checking

#### Repository Layer
- **RoleRepository**: CRUD operations for role metadata
- **Transactional Updates**: Proper SQLite/PostgreSQL support
- **Validation**: Name uniqueness, existence checks

### 5. Data Initialization ✅

#### Default Roles Seeding
- **File**: `src/db/seed.rs`
- **Features**:
  - Idempotent seeding (runs only if no roles exist)
  - Transactional consistency
  - Default roles: "Admin" (red) and "Member" (blue)
  - Integrated into application startup

#### Seeded Roles
1. **Admin Role**
   - Name: "Admin"
   - Description: "Full administrative access to the system"
   - Color: "#dc2626" (Red)

2. **Member Role**
   - Name: "Member"
   - Description: "Standard member access"
   - Color: "#2563eb" (Blue)

## Architecture Decisions

### Single Source of Truth
- **SpiceDB**: All authorization relationships and hierarchy
- **SQL Database**: Only metadata (names, colors, descriptions)
- **No Dual System**: Avoids consistency issues between systems

### Security First
- **ZedToken Sanitization**: Prevents internal tokens from leaking
- **mTLS Ready**: Infrastructure prepared for production security
- **Atomic Operations**: Prevents race conditions in hierarchy changes

### Development vs Production
- **Development**: Insecure SpiceDB connection with pre-shared key
- **Production Ready**: mTLS configuration structure in place
- **Feature Flags**: SQLite for development, PostgreSQL for production

## Current Status

### ✅ Completed
- Infrastructure setup with Docker Compose
- SpiceDB schema design and deployment scripts
- Core service architecture with placeholder implementations
- Complete API endpoint structure
- Role metadata CRUD operations
- ZedToken middleware for consistency
- Default role seeding
- Database schema with proper indexing and triggers

### 🚧 Placeholder Implementations
- SpiceDB client integration (dependency issues to resolve)
- Actual permission checking logic
- Role hierarchy operations
- Bulk relationship operations
- Member lookup and management

### 📋 Next Steps (Phase 2)
1. **Resolve SpiceDB Dependencies**: Fix `spicedb-rust` integration
2. **Implement Real SpiceDB Operations**: Replace placeholder methods
3. **Add mTLS Configuration**: Production security setup
4. **Comprehensive Testing**: Unit and integration tests
5. **Performance Optimization**: Batch operations and caching

## Usage

### Starting the System
```bash
# Start with Docker Compose (includes SpiceDB)
docker-compose up

# Or start Ki server only
cargo run
```

### Deploy SpiceDB Schema
```bash
# Make script executable
chmod +x deploy-schema.sh

# Deploy schema
./deploy-schema.sh
```

### Environment Variables
```bash
DATABASE_URL=sqlite:/app/data/ki.db
SPICEDB_ENDPOINT=localhost:50051
SPICEDB_TOKEN=somerandomkeyhere
PORT=3000
```

## Testing

### Role Metadata Operations
```bash
# Create a role
curl -X POST http://localhost:3000/api/roles \
  -H "Content-Type: application/json" \
  -d '{"name": "Developer", "description": "Development team", "color": "#10b981"}'

# List roles
curl http://localhost:3000/api/roles

# Get role details
curl http://localhost:3000/api/roles/{role-id}
```

### SpiceDB Operations (Placeholder)
```bash
# Get role tree (returns empty for now)
curl http://localhost:3000/api/roles/tree

# Check permissions (returns empty for now)
curl -X POST http://localhost:3000/api/permissions/check \
  -H "Content-Type: application/json" \
  -d '{"checks": []}'
```

## Files Modified/Created

### New Files
- `ki/schema.zed` - SpiceDB schema definition
- `ki/deploy-schema.sh` - Schema deployment script
- `ki/src/services/spicedb_service.rs` - SpiceDB service layer
- `ki/src/api/middleware/zed_token.rs` - ZedToken middleware
- `ki/src/api/middleware/mod.rs` - Middleware module
- `ki/src/db/models/role.rs` - Role data models
- `ki/src/db/repositories/role_repository.rs` - Role repository
- `ki/src/db/seed.rs` - Database seeding
- `ki/src/api/handlers/role_handlers.rs` - Role API handlers
- `ki/PHASE1_IMPLEMENTATION.md` - This documentation

### Modified Files
- `ki/Cargo.toml` - Added SpiceDB dependencies
- `ki/docker-compose.yml` - Added SpiceDB service
- `ki/src/main.rs` - Integrated seeding
- `ki/src/db/init.rs` - Added roles table
- `ki/src/db/models/mod.rs` - Added role model
- `ki/src/db/repositories/mod.rs` - Added role repository
- `ki/src/db/mod.rs` - Added seed module
- `ki/src/services/mod.rs` - Added SpiceDB service
- `ki/src/api/mod.rs` - Added middleware
- `ki/src/api/routes.rs` - Added role routes and middleware
- `ki/src/api/handlers/mod.rs` - Added role handlers

This completes Phase 1 of the SpiceDB roles and permissions system implementation. The foundation is now in place for Phase 2 testing and full SpiceDB integration.
