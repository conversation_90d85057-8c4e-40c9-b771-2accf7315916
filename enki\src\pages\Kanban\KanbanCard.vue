<script setup lang="ts">
import type { PropType } from 'vue';
import { Lock, Warning, User as UserIcon, Setting } from '@element-plus/icons-vue';
import Subtasks from '@/components/Subtasks.vue';
import TaskActions from '@/components/TaskActions.vue';
import { useKanbanStore } from '@/stores/kanbanStore';
import { useUsersStore } from '@/stores/usersStore';
import { useAgentsStore } from '@/stores/agentsStore';
import type { Task } from '@/types';

defineProps({
    content: {
        type: Object as PropType<Task>,
        required: true
    },
});

const emit = defineEmits<{
        (e: 'edit-task', task: Task): void;
        (e: 'delete-task', taskId: string): void;
}>();

const kanbanStore = useKanbanStore();
const usersStore = useUsersStore();
const agentsStore = useAgentsStore();

const editTask = (task: Task) => emit('edit-task', task);
const deleteTask = (taskId: string) => emit('delete-task', taskId);

function getAssigneeInfo(assigneeId: string) {
    // First check if it's a user
    const user = usersStore.getUser(assigneeId);
    if (user) {
        return {
            type: 'user' as const,
            name: user.display_name ?? user.email,
            avatar: user.photo_url,
            fallback: user.display_name?.charAt(0) || user.email.charAt(0)
        };
    }

    // Then check if it's an agent
    const agent = agentsStore.getAgent(assigneeId);
    if (agent) {
        return {
            type: 'agent' as const,
            name: agent.name,
            avatar: null,
            fallback: agent.name.charAt(0)
        };
    }

    // Fallback for unknown assignee
    return {
        type: 'unknown' as const,
        name: assigneeId,
        avatar: null,
        fallback: assigneeId.charAt(0)
    };
}
</script>

<template>
    <el-card class="task-card" hover>
        <template #header>
            <div class="task-content">
                <router-link :to="content.id">{{ content.name }}</router-link>
            </div>
            <TaskActions @editTask="editTask(content)" @deleteTask="deleteTask(content.id)" />
        </template>
        <template #default>
            <div v-if="content.description">{{ content.description }}</div>
            <Subtasks :taskId="content.id" @editTask="editTask" @deleteTask="deleteTask" />
            <div v-if="content.tags.length" class="flex">
                <el-tag v-for="tag in content.tags" :key="tag">{{ tag }}</el-tag>
            </div>
            <div v-if="content.blocking.length || content.blocked_by.length" class="dependencies">
                <div v-if="content.blocking.length" class="blocking">
                    <el-icon><Lock /></el-icon>
                    <span>Blocking {{ content.blocking.length }} task(s)</span>
                </div>
                <div v-if="content.blocked_by.length" class="blocked-by">
                    <el-icon><Warning /></el-icon>
                    <span>Blocked by {{ content.blocked_by.length }} task(s)</span>
                </div>
            </div>
        </template>
        <template #footer v-if="content.assignees.length || content.start_time || content.end_time">
            <div class="assignees-container">
                <div v-for="assigneeId in content.assignees" :key="assigneeId" class="assignee-item">
                    <el-avatar
                        :src="getAssigneeInfo(assigneeId).avatar"
                        :size="24"
                        :style="getAssigneeInfo(assigneeId).type === 'agent' ? 'background-color: #409eff;' : ''"
                    >
                        <el-icon v-if="getAssigneeInfo(assigneeId).type === 'agent'">
                            <Setting />
                        </el-icon>
                        <el-icon v-else-if="getAssigneeInfo(assigneeId).type === 'user' && !getAssigneeInfo(assigneeId).avatar">
                            <UserIcon />
                        </el-icon>
                        <span v-else>{{ getAssigneeInfo(assigneeId).fallback }}</span>
                    </el-avatar>
                    <span class="assignee-name">{{ getAssigneeInfo(assigneeId).name }}</span>
                </div>
            </div>
            <div class="flex">
                <el-date-picker
                    class="date-picker"
                    v-model="content.start_time"
                    type="datetime"
                    size="small"
                    format="DD/MM/YY HH:mm"
                    placeholder="Start date"
                    @change="kanbanStore.updateTask(content)"
                />
                <el-date-picker
                    class="date-picker"
                    v-model="content.end_time"
                    type="datetime"
                    size="small"
                    format="DD/MM/YY HH:mm"
                    placeholder="End date"
                    @change="kanbanStore.updateTask(content)"
                />
            </div>
        </template>
    </el-card>
</template>

<style scoped>
.task-card {
	margin-bottom: 8px;
}

:deep(.el-card__header),
:deep(.el-card__body),
:deep(.el-card__footer) {
    padding: 8px;
}

:deep(.el-card__body) {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

:deep(.el-card__header) {
	display: flex;
	justify-content: space-between;
	cursor: pointer;
}

.dependencies {
    display: flex;
    flex-direction: column;
    gap: 4px;
    font-size: 12px;
    color: #666;
}

.blocking {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #f56c6c;
}

.blocked-by {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #e6a23c;
}

.task-content {
	word-break: break-word;
    align-self: center;
}

.assignees-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 8px;
}

.assignee-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
}

.assignee-name {
    color: #666;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

:deep(.el-date-editor) {
    width: unset;
    padding: 0;
}

:deep(.el-date-editor .el-input__prefix-inner) {
    display: none;
}

:deep(.el-date-editor .el-icon) {
    margin-left: 0;
}
</style>