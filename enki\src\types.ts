export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type Column = {
	id: TaskStatus;
	name: string;
}

export type TaskStatus = 'todo' | 'doing' | 'review' | 'done';

export type Task = {
	id: string;
	name: string;
	status: TaskStatus;
	description?: string;
	tags: string[];
	assignees: string[];
	start_time?: string;
	end_time?: string;
	parent_task_id: string | undefined;
	position: number;
	blocking: string[];
	blocked_by: string[];
	created_at?: string;
	updated_at?: string;
}

export type Project = Task & { parent_task_id: undefined };

export type SpaceDefinition = {
    name: string;
    server: string;
}

export type Space = {
    projects: Project[];
}

export type TaskUpdateResponse = {
    primary_task: Task;
    affected_tasks: Task[];
}

export type User = {
    id: string;
    user_id: string;
    email: string;
    display_name?: string;
    photo_url?: string;
    created_at: string;
    updated_at: string;
    last_login?: string;
}

export type Agent = {
    id: string;
    name: string;
    description?: string;
    parameters: AgentParameters;
    created_by: string;
    created_at: string;
    updated_at: string;
    is_active: boolean;
}

export type AgentParameters = {
    model_type: string;
    temperature?: number;
    max_tokens?: number;
    system_prompt?: string;
    tools: string[];
    custom_config: Record<string, any>;
}

export type Permission = {
    id: string;
    entity_type: 'user' | 'agent';
    entity_id: string;
    resource_type: 'task' | 'project' | 'user' | 'agent' | 'system';
    resource_id?: string;
    permission_level: 'read' | 'write' | 'admin';
    granted_by: string;
    created_at: string;
}