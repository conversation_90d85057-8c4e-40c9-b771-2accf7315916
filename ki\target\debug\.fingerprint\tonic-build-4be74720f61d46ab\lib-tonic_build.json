{"rustc": 10895048813736897673, "features": "[\"default\", \"prost\", \"prost-build\", \"transport\"]", "declared_features": "[\"cleanup-markdown\", \"default\", \"prost\", \"prost-build\", \"transport\"]", "target": 9025750215440372010, "profile": 2241668132362809309, "path": 4067448470675943552, "deps": [[2739579679802620019, "prost_build", false, 15481979476127880898], [8549471757621926118, "prettyplease", false, 9538136473899354907], [8986759836770526006, "syn", false, 16227812344491362932], [12410540580958238005, "proc_macro2", false, 17053340830393542494], [16470553738848018267, "prost_types", false, 953804359540040425], [17990358020177143287, "quote", false, 12172367782111389661]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tonic-build-4be74720f61d46ab\\dep-lib-tonic_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}