use axum::{
    extract::State,
    http::{header, HeaderMap, StatusCode},
};
use axum_extra::extract::cookie::<PERSON>ieJar;
use rs_firebase_admin_sdk::auth::token::{error::TokenVerificationError, jwt::<PERSON><PERSON><PERSON><PERSON>, TokenVerifier};
use tracing::{info, warn};

use crate::{
    ServerState,
    api::auth::create_session_cookie,
    db::{
        models::NewSession,
        repositories::SessionRepository,
    },
    services::user_service::UserService,
};

pub async fn connect(
    State(state): State<ServerState>,
    headers: HeaderMap,
    jar: <PERSON>ieJar,
) -> Result<(<PERSON><PERSON><PERSON><PERSON>, String), (StatusCode, String)> {
    // Extract the token from the Authorization header
    let auth_header = headers.get(header::AUTHORIZATION)
        .and_then(|header| header.to_str().ok());

    let token = match auth_header {
        Some(header) if header.starts_with("Bearer ") => {
            header.trim_start_matches("Bearer ").to_owned()
        }
        _ => {
            warn!("Missing or invalid Authorization header");
            return Err((StatusCode::UNAUTHORIZED, "Missing or invalid Authorization header".to_string()));
        }
    };

    // Verify the Firebase token
    let verifier = state.verifier.clone();

    match verifier.verify_token(&token).await {
        Ok(firebase_token) => {
            let user_id = firebase_token.critical_claims.sub.clone();

            // Ensure user exists in database (create if needed)
            let user_service = UserService::new(state.db.clone());
            match user_service.ensure_user_exists(&firebase_token).await {
                Ok(_user) => {
                    info!("User {} ensured in database", user_id);
                },
                Err(e) => {
                    warn!("Failed to ensure user exists: {}", e);
                    // Continue with session creation even if user creation fails
                    // This maintains backward compatibility
                }
            }

            // Create a new session
            let session_repo = SessionRepository::new(state.db.clone());

            // Generate a secure random token
            let session_token = SessionRepository::generate_token();

            // Calculate expiration time
            let expires_at = SessionRepository::calculate_expiration();

            // Create a new session in the database
            let new_session = NewSession {
                user_id: user_id.clone(),
                token: session_token.clone(),
                expires_at,
            };

            match session_repo.create(new_session).await {
                Ok(_) => {
                    info!("Created session for user {}", user_id);

                    // Create a session cookie
                    let cookie = create_session_cookie(&session_token);

                    // Add the cookie to the jar
                    let jar_with_cookie = jar.add(cookie);

                    // Return the jar with the cookie and the user ID
                    Ok((jar_with_cookie, user_id))
                },
                Err(e) => {
                    warn!("Failed to create session: {}", e);
                    Err((StatusCode::INTERNAL_SERVER_ERROR, "Failed to create session".to_string()))
                }
            }
        },
        Err(err) => {
            // Check if the error is "Token was issued in the future"
            if let TokenVerificationError::IssuedInFuture = *err.current_context() {
                // If the error is due to clock skew, we'll log it and accept the token anyway
                warn!("Token was issued in the future. This is likely due to clock skew. Attempting to parse token anyway.");

                // Parse the token manually to extract the claims
                match JWToken::from_encoded(&token) {
                    Ok(firebase_token) => {
                        let user_id = firebase_token.critical_claims.sub.clone();

                        // Ensure user exists in database (create if needed)
                        let user_service = UserService::new(state.db.clone());
                        match user_service.ensure_user_exists(&firebase_token).await {
                            Ok(_user) => {
                                info!("User {} ensured in database", user_id);
                            },
                            Err(e) => {
                                warn!("Failed to ensure user exists: {}", e);
                                // Continue with session creation even if user creation fails
                                // This maintains backward compatibility
                            }
                        }

                        // Create a new session
                        let session_repo = SessionRepository::new(state.db.clone());

                        // Generate a secure random token
                        let session_token = SessionRepository::generate_token();

                        // Calculate expiration time
                        let expires_at = SessionRepository::calculate_expiration();

                        // Create a new session in the database
                        let new_session = NewSession {
                            user_id: user_id.clone(),
                            token: session_token.clone(),
                            expires_at,
                        };

                        match session_repo.create(new_session).await {
                            Ok(_) => {
                                info!("Created session for user {}", user_id);

                                // Create a session cookie
                                let cookie = create_session_cookie(&session_token);

                                // Add the cookie to the jar
                                let jar_with_cookie = jar.add(cookie);

                                // Return the jar with the cookie and the user ID
                                Ok((jar_with_cookie, user_id))
                            },
                            Err(e) => {
                                warn!("Failed to create session: {}", e);
                                Err((StatusCode::INTERNAL_SERVER_ERROR, "Failed to create session".to_string()))
                            }
                        }
                    },
                    Err(_) => {
                        warn!("Failed to parse token after 'issued in future' error");
                        Err((StatusCode::UNAUTHORIZED, "Invalid token".to_string()))
                    }
                }
            } else {
                warn!("Token verification failed: {}", err);
                Err((StatusCode::UNAUTHORIZED, err.to_string()))
            }
        }
    }
}
