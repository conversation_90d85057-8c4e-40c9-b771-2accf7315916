definition user {}

definition role {
    relation member: user
    // A role can have at most one parent to enforce a tree structure,
    // which simplifies cycle prevention.
    relation parent: role 
    
    // This permission correctly computes transitive membership.
    // A user is a member of this role if they are a direct member
    // OR a member of any parent role, recursively.
    permission all_members = member + parent->all_members
}

definition task {
    // Relations are split by type for schema correctness. A permission
    // cannot be a subject type, so we relate to roles directly.
    relation owner_user: user
    relation owner_role: role
    relation assignee: user
    relation viewer_user: user
    relation viewer_role: role
    relation editor_user: user
    relation editor_role: role
    relation admin_user: user
    relation admin_role: role

    // Permissions use arrow notation to traverse relations to roles
    // and check against their computed 'all_members' permission.
    permission owner = owner_user + owner_role->all_members
    permission viewer = viewer_user + viewer_role->all_members
    permission editor = editor_user + editor_role->all_members
    permission admin = admin_user + admin_role->all_members

    permission view = viewer + editor + admin + owner + assignee
    permission edit = editor + admin + owner
    permission delete = admin + owner
}

definition global_settings {
    relation owner_user: user
    relation owner_role: role
    relation admin_user: user
    relation admin_role: role

    permission owner = owner_user + owner_role->all_members
    permission admin = admin_user + admin_role->all_members

    permission manage_roles = admin + owner
    permission invite_users = admin + owner
}
