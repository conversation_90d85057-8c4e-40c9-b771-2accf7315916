K:\Prog\enki-mr\ki\target\debug\ki.exe: K:\Prog\enki-mr\ki\src\api\auth.rs K:\Prog\enki-mr\ki\src\api\handlers\agent_handlers.rs K:\Prog\enki-mr\ki\src\api\handlers\firebase_handlers.rs K:\Prog\enki-mr\ki\src\api\handlers\mod.rs K:\Prog\enki-mr\ki\src\api\handlers\permission_handlers.rs K:\Prog\enki-mr\ki\src\api\handlers\role_handlers.rs K:\Prog\enki-mr\ki\src\api\handlers\task_handlers.rs K:\Prog\enki-mr\ki\src\api\handlers\user_handlers.rs K:\Prog\enki-mr\ki\src\api\middleware\mod.rs K:\Prog\enki-mr\ki\src\api\middleware\zed_token.rs K:\Prog\enki-mr\ki\src\api\mod.rs K:\Prog\enki-mr\ki\src\api\routes.rs K:\Prog\enki-mr\ki\src\db\connection.rs K:\Prog\enki-mr\ki\src\db\init.rs K:\Prog\enki-mr\ki\src\db\mod.rs K:\Prog\enki-mr\ki\src\db\models\agent.rs K:\Prog\enki-mr\ki\src\db\models\mod.rs K:\Prog\enki-mr\ki\src\db\models\permission.rs K:\Prog\enki-mr\ki\src\db\models\role.rs K:\Prog\enki-mr\ki\src\db\models\session.rs K:\Prog\enki-mr\ki\src\db\models\task.rs K:\Prog\enki-mr\ki\src\db\models\user.rs K:\Prog\enki-mr\ki\src\db\repositories\agent_repository.rs K:\Prog\enki-mr\ki\src\db\repositories\mod.rs K:\Prog\enki-mr\ki\src\db\repositories\permission_repository.rs K:\Prog\enki-mr\ki\src\db\repositories\role_repository.rs K:\Prog\enki-mr\ki\src\db\repositories\session_repository.rs K:\Prog\enki-mr\ki\src\db\repositories\task_repository.rs K:\Prog\enki-mr\ki\src\db\repositories\user_repository.rs K:\Prog\enki-mr\ki\src\db\seed.rs K:\Prog\enki-mr\ki\src\main.rs K:\Prog\enki-mr\ki\src\services\mod.rs K:\Prog\enki-mr\ki\src\services\spicedb_service.rs K:\Prog\enki-mr\ki\src\services\user_service.rs
