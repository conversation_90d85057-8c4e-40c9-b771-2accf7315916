[package]
name = "ki"
version = "0.1.0"
edition = "2021"
default-run = "ki"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[features]
default = ["sqlite"]
sqlite = ["sqlx/sqlite"]
postgres = ["sqlx/postgres"]

[dependencies]
axum = "0.8.3"
reqwest = "0.12.15"
rs-firebase-admin-sdk = { version = "2.4.0", features = ["tokens"] }
tokio = { version = "1.36.0", features = ["full"] }
serde = { version = "1.0.197", features = ["derive"] }
serde_json = "1.0.114"
tracing = "0.1.40"
tracing-subscriber = "0.3.18"

# Database
sqlx = { version = "0.8.5", features = ["runtime-tokio", "tls-rustls", "uuid", "chrono", "json", "migrate"] }

# SpiceDB
spicedb-grpc = "0.1.1"
tonic = { version = "0.12.3", features = ["tls", "channel"] }
prost = "0.13.1"
prost-types = "0.13.1"

# Utilities
uuid = { version = "1.7.0", features = ["v4", "serde"] }
chrono = { version = "0.4.35", features = ["serde"] }
tower = "0.4.13"
tower-http = { version = "0.6.2", features = ["cors", "trace"] }
dotenv = "0.15.0"
anyhow = "1.0.80"
thiserror = "1.0.57"
async-trait = "0.1.88"
rand = "0.8.5"
axum-extra = { version = "0.10.1", features = ["cookie"] }
