use axum::{
    extract::{Path, Query, State},
    http::StatusC<PERSON>,
    Json,
};
use serde::Deserialize;
use uuid::Uuid;
use crate::{
    db::{
        models::{EntityType, NewPermission, PermissionResponse, ResourceType, UpdatePermission},
        repositories::PermissionRepository,
    },
    ServerState,
};

#[derive(Deserialize)]
pub struct PermissionQueryParams {
    entity_type: Option<String>,
    entity_id: Option<String>,
    resource_type: Option<String>,
    resource_id: Option<String>,
}

/// Get all permissions
pub async fn get_permissions(
    State(server_state): State<ServerState>,
    Query(params): Query<PermissionQueryParams>,
) -> Result<(StatusCode, Json<Vec<PermissionResponse>>), (StatusCode, String)> {
    let repo = PermissionRepository::new(server_state.db);

    let permissions = if let (Some(entity_type_str), Some(entity_id)) = (params.entity_type, params.entity_id) {
        let entity_type = EntityType::from(entity_type_str.as_str());
        repo.get_by_entity(entity_type, &entity_id).await
    } else if let Some(resource_type_str) = params.resource_type {
        let resource_type = ResourceType::from(resource_type_str.as_str());
        repo.get_by_resource(resource_type, params.resource_id.as_deref()).await
    } else {
        repo.get_all().await
    };

    match permissions {
        Ok(permissions) => {
            let permission_responses: Vec<PermissionResponse> = permissions.into_iter().map(|permission| permission.into()).collect();
            Ok((StatusCode::OK, Json(permission_responses)))
        }
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Create a new permission
pub async fn create_permission(
    State(server_state): State<ServerState>,
    Json(new_permission): Json<NewPermission>,
) -> Result<(StatusCode, Json<PermissionResponse>), (StatusCode, String)> {
    let repo = PermissionRepository::new(server_state.db);

    match repo.create(new_permission).await {
        Ok(permission) => Ok((StatusCode::CREATED, Json(permission.into()))),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Update a permission
pub async fn update_permission(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
    Json(update_permission): Json<UpdatePermission>,
) -> Result<(StatusCode, Json<PermissionResponse>), (StatusCode, String)> {
    let repo = PermissionRepository::new(server_state.db);

    let permission_id = Uuid::parse_str(&id)
        .map_err(|_| (StatusCode::BAD_REQUEST, "Invalid permission ID format".to_string()))?;

    match repo.update(permission_id, update_permission).await {
        Ok(permission) => Ok((StatusCode::OK, Json(permission.into()))),
        Err(e) => {
            if e.to_string().contains("not found") {
                Err((StatusCode::NOT_FOUND, "Permission not found".to_string()))
            } else {
                Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string()))
            }
        }
    }
}

/// Delete a permission
pub async fn delete_permission(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<StatusCode, (StatusCode, String)> {
    let repo = PermissionRepository::new(server_state.db);

    let permission_id = Uuid::parse_str(&id)
        .map_err(|_| (StatusCode::BAD_REQUEST, "Invalid permission ID format".to_string()))?;

    match repo.delete(permission_id).await {
        Ok(()) => Ok(StatusCode::NO_CONTENT),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}
