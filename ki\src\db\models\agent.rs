use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::{types::<PERSON><PERSON>, FromRow};
use uuid::Uuid;

/// Agent configuration parameters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentParameters {
    pub model_type: String,
    pub temperature: Option<f32>,
    pub max_tokens: Option<i32>,
    pub system_prompt: Option<String>,
    pub tools: Vec<String>,
    pub custom_config: serde_json::Value,
}

impl Default for AgentParameters {
    fn default() -> Self {
        Self {
            model_type: "claude-3-sonnet".to_string(),
            temperature: Some(0.7),
            max_tokens: Some(4096),
            system_prompt: None,
            tools: vec![],
            custom_config: serde_json::Value::Object(serde_json::Map::new()),
        }
    }
}

/// Agent model
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Agent {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub parameters: Json<AgentParameters>,
    pub created_by: String, // Firebase user ID
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_active: bool,
}

/// New agent request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewAgent {
    pub name: String,
    pub description: Option<String>,
    pub parameters: AgentParameters,
    pub created_by: String, // Firebase user ID
    pub is_active: Option<bool>,
}

/// Update agent request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateAgent {
    pub name: Option<String>,
    pub description: Option<String>,
    pub parameters: Option<AgentParameters>,
    pub is_active: Option<bool>,
}

/// Agent response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentResponse {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub parameters: AgentParameters,
    pub created_by: String,
    pub created_at: String,
    pub updated_at: String,
    pub is_active: bool,
}

impl From<Agent> for AgentResponse {
    fn from(agent: Agent) -> Self {
        Self {
            id: agent.id.to_string(),
            name: agent.name,
            description: agent.description,
            parameters: agent.parameters.0,
            created_by: agent.created_by,
            created_at: agent.created_at.to_rfc3339(),
            updated_at: agent.updated_at.to_rfc3339(),
            is_active: agent.is_active,
        }
    }
}
