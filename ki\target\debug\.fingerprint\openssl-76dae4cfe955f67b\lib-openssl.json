{"rustc": 10895048813736897673, "features": "[\"default\"]", "declared_features": "[\"aws-lc\", \"bindgen\", \"default\", \"unstable_boringssl\", \"v101\", \"v102\", \"v110\", \"v111\", \"vendored\"]", "target": 17474193825155910204, "profile": 2241668132362809309, "path": 13607162284785985193, "deps": [[3722963349756955755, "once_cell", false, 12825410154089437232], [6166349630582887940, "bitflags", false, 1069913959554015464], [6635237767502169825, "foreign_types", false, 11963648273702022962], [8194304432723500424, "libc", false, 18344364001227924756], [10099563100786658307, "openssl_macros", false, 2537522022157820149], [10411997081178400487, "cfg_if", false, 234038572160536405], [12853863448654256444, "build_script_build", false, 240866318518444856], [16114643047409176557, "ffi", false, 13548721750723708415]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\openssl-76dae4cfe955f67b\\dep-lib-openssl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}