<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Plus, User as UserIcon, Setting } from '@element-plus/icons-vue';
import { useUsersStore } from '@/stores/usersStore';
import { useAgentsStore } from '@/stores/agentsStore';
import DialogUser from '@/components/DialogUser.vue';
import DialogAgent from '@/components/DialogAgent.vue';
import type { User as UserType, Agent } from '@/types';

const router = useRouter();
const usersStore = useUsersStore();
const agentsStore = useAgentsStore();

const showUserDialog = ref(false);
const showAgentDialog = ref(false);
const editingUser = ref<UserType | undefined>(undefined);
const editingAgent = ref<Agent | undefined>(undefined);

onMounted(async () => {
    try {
        await Promise.all([
            usersStore.fetchUsers(),
            agentsStore.fetchAgents()
        ]);
    } catch (error) {
        ElMessage.error('Failed to load contacts');
        console.error('Error loading contacts:', error);
    }
});

const handleUserClick = (user: UserType) => {
    router.push(`/profile/user/${user.id}`);
};

const handleAgentClick = (agent: Agent) => {
    router.push(`/profile/agent/${agent.id}`);
};

const handleEditUser = (user: UserType) => {
    editingUser.value = user;
    showUserDialog.value = true;
};

const handleEditAgent = (agent: Agent) => {
    editingAgent.value = agent;
    showAgentDialog.value = true;
};

const handleDeleteUser = async (user: UserType) => {
    try {
        await usersStore.deleteUser(user.id);
        ElMessage.success('User deleted successfully');
    } catch (error) {
        ElMessage.error('Failed to delete user');
        console.error('Error deleting user:', error);
    }
};

const handleDeleteAgent = async (agent: Agent) => {
    try {
        await agentsStore.deleteAgent(agent.id);
        ElMessage.success('Agent deleted successfully');
    } catch (error) {
        ElMessage.error('Failed to delete agent');
        console.error('Error deleting agent:', error);
    }
};

const handleNewUser = () => {
    editingUser.value = undefined;
    showUserDialog.value = true;
};

const handleNewAgent = () => {
    editingAgent.value = undefined;
    showAgentDialog.value = true;
};

const onUserDialogClose = () => {
    showUserDialog.value = false;
    editingUser.value = undefined;
};

const onAgentDialogClose = () => {
    showAgentDialog.value = false;
    editingAgent.value = undefined;
};

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
};

const getStatusColor = (isActive: boolean) => {
    return isActive ? 'success' : 'info';
};

const getStatusText = (isActive: boolean) => {
    return isActive ? 'Active' : 'Inactive';
};
</script>

<template>
    <div class="contacts-container">
        <el-row :gutter="24">
            <!-- Users Section -->
            <el-col :span="12">
                <el-card class="contacts-card">
                    <template #header>
                        <div class="card-header">
                            <div class="header-title">
                                <el-icon><UserIcon /></el-icon>
                                <span>Users</span>
                            </div>
                            <el-button type="primary" size="small" @click="handleNewUser">
                                <el-icon><Plus /></el-icon>
                                Add User
                            </el-button>
                        </div>
                    </template>

                    <div v-loading="usersStore.loading" class="contacts-list">
                        <div v-if="usersStore.error" class="error-message">
                            <el-alert :title="usersStore.error" type="error" :closable="false" />
                        </div>

                        <div v-else-if="usersStore.users.length === 0" class="empty-state">
                            <el-empty description="No users found" />
                        </div>

                        <div v-else class="user-list">
                            <div
                                v-for="user in usersStore.users"
                                :key="user.id"
                                class="contact-item"
                                @click="handleUserClick(user)"
                            >
                                <div class="contact-avatar">
                                    <el-avatar :src="user.photo_url" :size="40">
                                        {{ user.display_name?.charAt(0) || user.email.charAt(0) }}
                                    </el-avatar>
                                </div>
                                <div class="contact-info">
                                    <div class="contact-name">{{ user.display_name || user.email }}</div>
                                    <div class="contact-email">{{ user.email }}</div>
                                    <div class="contact-meta">
                                        <span>Joined: {{ formatDate(user.created_at) }}</span>
                                        <span v-if="user.last_login">
                                            • Last login: {{ formatDate(user.last_login) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="contact-actions">
                                    <el-button size="small" @click.stop="handleEditUser(user)">
                                        Edit
                                    </el-button>
                                    <el-popconfirm
                                        title="Are you sure you want to delete this user?"
                                        @confirm="handleDeleteUser(user)"
                                    >
                                        <template #reference>
                                            <el-button size="small" type="danger" @click.stop>
                                                Delete
                                            </el-button>
                                        </template>
                                    </el-popconfirm>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-card>
            </el-col>

            <!-- Agents Section -->
            <el-col :span="12">
                <el-card class="contacts-card">
                    <template #header>
                        <div class="card-header">
                            <div class="header-title">
                                <el-icon><Setting /></el-icon>
                                <span>Agents</span>
                            </div>
                            <el-button type="primary" size="small" @click="handleNewAgent">
                                <el-icon><Plus /></el-icon>
                                Add Agent
                            </el-button>
                        </div>
                    </template>

                    <div v-loading="agentsStore.loading" class="contacts-list">
                        <div v-if="agentsStore.error" class="error-message">
                            <el-alert :title="agentsStore.error" type="error" :closable="false" />
                        </div>

                        <div v-else-if="agentsStore.agents.length === 0" class="empty-state">
                            <el-empty description="No agents found" />
                        </div>

                        <div v-else class="agent-list">
                            <div
                                v-for="agent in agentsStore.agents"
                                :key="agent.id"
                                class="contact-item"
                                @click="handleAgentClick(agent)"
                            >
                                <div class="contact-avatar">
                                    <el-avatar :size="40" style="background-color: #409eff;">
                                        <el-icon><Setting /></el-icon>
                                    </el-avatar>
                                </div>
                                <div class="contact-info">
                                    <div class="contact-name">{{ agent.name }}</div>
                                    <div class="contact-description">{{ agent.description || 'No description' }}</div>
                                    <div class="contact-meta">
                                        <el-tag :type="getStatusColor(agent.is_active)" size="small">
                                            {{ getStatusText(agent.is_active) }}
                                        </el-tag>
                                        <span>• Model: {{ agent.parameters.model_type }}</span>
                                        <span>• Created: {{ formatDate(agent.created_at) }}</span>
                                    </div>
                                </div>
                                <div class="contact-actions">
                                    <el-button size="small" @click.stop="handleEditAgent(agent)">
                                        Edit
                                    </el-button>
                                    <el-popconfirm
                                        title="Are you sure you want to delete this agent?"
                                        @confirm="handleDeleteAgent(agent)"
                                    >
                                        <template #reference>
                                            <el-button size="small" type="danger" @click.stop>
                                                Delete
                                            </el-button>
                                        </template>
                                    </el-popconfirm>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <!-- Dialogs -->
        <DialogUser
            v-model="showUserDialog"
            :user="editingUser"
            @close="onUserDialogClose"
        />
        <DialogAgent
            v-model="showAgentDialog"
            :agent="editingAgent"
            @close="onAgentDialogClose"
        />
    </div>
</template>

<style scoped>
.contacts-container {
    padding: 20px;
}

.contacts-card {
    height: calc(100vh - 200px);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
}

.contacts-list {
    height: calc(100vh - 280px);
    overflow-y: auto;
}

.contact-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid var(--el-border-color-light);
    cursor: pointer;
    transition: background-color 0.2s;
}

.contact-item:hover {
    background-color: var(--el-fill-color-light);
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-avatar {
    margin-right: 12px;
}

.contact-info {
    flex: 1;
    min-width: 0;
}

.contact-name {
    font-weight: 600;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.contact-email,
.contact-description {
    color: var(--el-text-color-regular);
    font-size: 14px;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.contact-meta {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.contact-actions {
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.2s;
}

.contact-item:hover .contact-actions {
    opacity: 1;
}

.error-message,
.empty-state {
    padding: 20px;
    text-align: center;
}
</style>
