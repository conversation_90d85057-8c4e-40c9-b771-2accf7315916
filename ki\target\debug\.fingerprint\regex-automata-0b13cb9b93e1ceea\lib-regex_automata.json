{"rustc": 10895048813736897673, "features": "[\"alloc\", \"meta\", \"nfa-pikevm\", \"nfa-thompson\", \"std\", \"syntax\", \"unicode-bool\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 4726246767843925232, "profile": 2241668132362809309, "path": 11702534307274980889, "deps": [[9408802513701742484, "regex_syntax", false, 5991740648143646527]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-automata-0b13cb9b93e1ceea\\dep-lib-regex_automata", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}