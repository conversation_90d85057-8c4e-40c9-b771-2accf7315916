use axum::{
    extract::{Path, State},
    http::StatusCode,
    Json,
};
use uuid::Uuid;
use crate::{
    db::{
        models::{NewU<PERSON>, NewManualUser, UpdateUser, UserResponse},
        repositories::UserRepository,
    },
    services::user_service::UserService,
    ServerState,
};

/// Get all users
pub async fn get_users(
    State(server_state): State<ServerState>,
) -> Result<(StatusC<PERSON>, Json<Vec<UserResponse>>), (StatusCode, String)> {
    let repo = UserRepository::new(server_state.db);

    match repo.get_all().await {
        Ok(users) => {
            let user_responses: Vec<UserResponse> = users.into_iter().map(|user| user.into()).collect();
            Ok((StatusCode::OK, Json(user_responses)))
        }
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get a user by ID
pub async fn get_user(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    let repo = UserRepository::new(server_state.db);

    let user_id = Uuid::parse_str(&id)
        .map_err(|_| (StatusCode::BAD_REQUEST, "Invalid user ID format".to_string()))?;

    match repo.get_by_id(user_id).await {
        Ok(Some(user)) => Ok((StatusCode::OK, Json(user.into()))),
        Ok(None) => Err((StatusCode::NOT_FOUND, "User not found".to_string())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Get a user by Firebase user ID
pub async fn get_user_by_firebase_id(
    State(server_state): State<ServerState>,
    Path(firebase_user_id): Path<String>,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    let repo = UserRepository::new(server_state.db);

    match repo.get_by_user_id(&firebase_user_id).await {
        Ok(Some(user)) => Ok((StatusCode::OK, Json(user.into()))),
        Ok(None) => Err((StatusCode::NOT_FOUND, "User not found".to_string())),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Create a new user
pub async fn create_user(
    State(server_state): State<ServerState>,
    Json(new_user): Json<NewUser>,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    let repo = UserRepository::new(server_state.db);

    match repo.create(new_user).await {
        Ok(user) => Ok((StatusCode::CREATED, Json(user.into()))),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Update a user
pub async fn update_user(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
    Json(update_user): Json<UpdateUser>,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    let repo = UserRepository::new(server_state.db);

    let user_id = Uuid::parse_str(&id)
        .map_err(|_| (StatusCode::BAD_REQUEST, "Invalid user ID format".to_string()))?;

    match repo.update(user_id, update_user).await {
        Ok(user) => Ok((StatusCode::OK, Json(user.into()))),
        Err(e) => {
            if e.to_string().contains("not found") {
                Err((StatusCode::NOT_FOUND, "User not found".to_string()))
            } else {
                Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string()))
            }
        }
    }
}

/// Delete a user
pub async fn delete_user(
    State(server_state): State<ServerState>,
    Path(id): Path<String>,
) -> Result<StatusCode, (StatusCode, String)> {
    let repo = UserRepository::new(server_state.db);

    let user_id = Uuid::parse_str(&id)
        .map_err(|_| (StatusCode::BAD_REQUEST, "Invalid user ID format".to_string()))?;

    match repo.delete(user_id).await {
        Ok(()) => Ok(StatusCode::NO_CONTENT),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Create or update user (upsert)
pub async fn upsert_user(
    State(server_state): State<ServerState>,
    Json(new_user): Json<NewUser>,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    let repo = UserRepository::new(server_state.db);

    match repo.upsert(new_user).await {
        Ok(user) => Ok((StatusCode::OK, Json(user.into()))),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}

/// Create a manual user (without Firebase ID)
pub async fn create_manual_user(
    State(server_state): State<ServerState>,
    Json(manual_user): Json<NewManualUser>,
) -> Result<(StatusCode, Json<UserResponse>), (StatusCode, String)> {
    let user_service = UserService::new(server_state.db);

    match user_service.create_manual_user(
        manual_user.email,
        manual_user.display_name,
        manual_user.photo_url,
    ).await {
        Ok(user) => Ok((StatusCode::CREATED, Json(user.into()))),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
    }
}
