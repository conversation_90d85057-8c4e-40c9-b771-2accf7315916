<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useUsersStore } from '@/stores/usersStore';
import type { User } from '@/types';

const props = defineProps<{
    user?: User;
}>();

const emit = defineEmits<{
    'close': [];
}>();

const usersStore = useUsersStore();

const userForm = ref({
    email: '',
    display_name: '',
    photo_url: ''
});

const formRef = ref();
const loading = ref(false);

const show = computed({
    get: () => {
        if (!props.user) return false;
        userForm.value = {
            email: props.user.email,
            display_name: props.user.display_name || '',
            photo_url: props.user.photo_url || ''
        };
        return true;
    },
    set: (_) => resetForm()
});

const isEditing = computed(() => !!props.user);
const dialogTitle = computed(() => isEditing.value ? 'Edit User' : 'Add New User');

const rules = {
    email: [
        { required: true, message: 'Email is required', trigger: 'blur' },
        { type: 'email', message: 'Please enter a valid email address', trigger: 'blur' }
    ]
};

const resetForm = () => {
    userForm.value = {
        email: '',
        display_name: '',
        photo_url: ''
    };
    formRef.value?.clearValidate();
};

const handleClose = () => {
    resetForm();
    emit('close');
};

const handleSave = async () => {
    if (!formRef.value) return;

    try {
        await formRef.value.validate();
        loading.value = true;

        const userData = {
            email: userForm.value.email,
            display_name: userForm.value.display_name || undefined,
            photo_url: userForm.value.photo_url || undefined
        };

        if (isEditing.value && props.user) {
            // For editing, we need to include the user_id and other fields
            const fullUserData = {
                ...props.user,
                ...userData
            };
            await usersStore.updateUser(props.user.id, fullUserData);
            ElMessage.success('User updated successfully');
        } else {
            // For creating new users, use the manual user creation endpoint
            await usersStore.createManualUser(userData);
            ElMessage.success('User created successfully');
        }

        handleClose();
    } catch (error) {
        console.error('Error saving user:', error);
        ElMessage.error(isEditing.value ? 'Failed to update user' : 'Failed to create user');
    } finally {
        loading.value = false;
    }
};
</script>

<template>
    <el-dialog
        v-model="show"
        :title="dialogTitle"
        width="500px"
        @close="handleClose"
    >
        <el-form
            ref="formRef"
            :model="userForm"
            :rules="rules"
            label-width="120px"
            label-position="left"
        >
            <!-- Firebase User ID is automatically managed for new users -->
            <div v-if="isEditing" class="form-help-text" style="margin-bottom: 16px;">
                <strong>Firebase User ID:</strong> {{ props.user?.user_id }}
                <br>
                <small>Firebase user ID is automatically managed and cannot be changed.</small>
            </div>

            <el-form-item label="Email" prop="email">
                <el-input
                    v-model="userForm.email"
                    type="email"
                    placeholder="Enter email address"
                />
            </el-form-item>

            <el-form-item label="Display Name">
                <el-input
                    v-model="userForm.display_name"
                    placeholder="Enter display name (optional)"
                />
            </el-form-item>

            <el-form-item label="Photo URL">
                <el-input
                    v-model="userForm.photo_url"
                    placeholder="Enter photo URL (optional)"
                />
                <div class="form-help-text">
                    URL to the user's profile picture
                </div>
            </el-form-item>

            <el-form-item v-if="userForm.photo_url" label="Preview">
                <el-avatar :src="userForm.photo_url" :size="60">
                    {{ userForm.display_name?.charAt(0) || userForm.email.charAt(0) }}
                </el-avatar>
            </el-form-item>
        </el-form>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">Cancel</el-button>
                <el-button
                    type="primary"
                    :loading="loading"
                    @click="handleSave"
                >
                    {{ isEditing ? 'Update' : 'Create' }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<style scoped>
.form-help-text {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}
</style>
