<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { Edit } from '@element-plus/icons-vue';
import { useDateFormat } from '@vueuse/core';
import { useKanbanStore } from '@/stores/kanbanStore';
import DialogTask from '@/components/DialogTask.vue';
import KanbanColumn from './KanbanColumn.vue';
import type { Optional, Task, TaskStatus } from '@/types';
import { useUsersStore } from '@/stores/usersStore';
import { useAgentsStore } from '@/stores/agentsStore';

const props = defineProps<{
  projectId: string;
}>();

const router = useRouter();
const kanbanStore = useKanbanStore();
const usersStore = useUsersStore();
const agentsStore = useAgentsStore();

// State
const loading = computed(() => kanbanStore.loading);
const error = computed(() => kanbanStore.error);
const columns = computed(() => kanbanStore.columns);
const currentProjectId = computed(() => kanbanStore.currentProjectId);
const currentProject = computed(() => kanbanStore.getCurrentProject);

// Get tasks by column ID (status)
const getTasksByColumnId = (columnId: TaskStatus) => {
  	return kanbanStore.getTasksByStatus(props.projectId, columnId);
};

// Task dialog
const editingTask = ref<Optional<Task, 'id'> | undefined>();

// Task methods
function addTask(columnId: TaskStatus) {
	editingTask.value = {
		name: '',
		description: '',
		status: columnId,
		position: getTasksByColumnId(columnId).length,
		tags: [],
		assignees: [],
		start_time: undefined,
		end_time: undefined,
		parent_task_id: currentProjectId.value,
		blocking: [],
		blocked_by: [],
	};
}

function editTask(task: Task) {
	editingTask.value = task;
}

// Delete project dialog
const taskToDelete = ref<string>();
const showDeleteDialog = computed({
	get: () => taskToDelete.value !== undefined,
	set: () => { return; }
});
const projectDeleteMessage = ref('');

function confirmDeleteTask(taskId: string) {
	const subTasks = kanbanStore.getSubTasks(taskId);
	if (subTasks.length > 0) {
			projectDeleteMessage.value = `This task has ${subTasks.length} task${subTasks.length > 1 ? 's' : ''}. Delete them as well?`;
	} else {
			projectDeleteMessage.value = 'Delete this task?';
	}
	taskToDelete.value = taskId;
}

async function deleteTask() {
	if (!taskToDelete.value) return;
	let navigate = currentProject.value?.parent_task_id;
	await kanbanStore.deleteTask(taskToDelete.value);
	if (currentProjectId.value === taskToDelete.value)
		router.push(
			navigate
				? { name: 'Kanban', params: { projectId: navigate } }
				: { name: 'Dashboard' }
		);
	taskToDelete.value = undefined;
}

async function handleMoveTask(taskId: string, newColumnId: string, newPosition: number) {
	await kanbanStore.moveTask(taskId, newColumnId as TaskStatus, newPosition);
}

function getAssigneeName(assigneeId: string) {
	// First check if it's a user
	const user = usersStore.getUser(assigneeId);
	if (user) {
		return user.display_name ?? user.email;
	}

	// Then check if it's an agent
	const agent = agentsStore.getAgent(assigneeId);
	if (agent) {
		return agent.name;
	}

	// Fallback for unknown assignee
	return assigneeId;
}

// Watch for route changes to update the current project
watch(() => props.projectId, async (newProjectId) => {
	if (newProjectId && newProjectId !== currentProjectId.value) {
		await kanbanStore.setCurrentProject(newProjectId);
	}
}, { immediate: true });
</script>

<template>
	<div class="kanban-container">
		<div v-if="loading" class="loading-container">
			<el-skeleton :rows="3" animated />
		</div>
		<div v-else-if="error" class="error-container">
			<el-alert :title="error" type="error" :closable="false" show-icon />
		</div>
		<div v-else class="kanban-board">
			<div class="kanban-header" v-if="currentProject">
				<el-descriptions
					:title="currentProject.name"
					:column="2"
					border
				>
					<template #extra>
						<el-button type="primary" size="small" @click="editTask(currentProject)">
							<el-icon><Edit /></el-icon>
							Edit Project
						</el-button>
						<el-button type="danger" size="small" @click="confirmDeleteTask(currentProject.id)" style="margin-left: 8px;">
							Delete Project
						</el-button>
					</template>
					<el-descriptions-item label="Description" :rowspan="4">{{ currentProject.description }}</el-descriptions-item>
					<el-descriptions-item label="Tags" v-if="currentProject.tags.length">
						<el-tag v-for="tag in currentProject.tags" :key="tag" size="small" style="margin-right: 8px;">
							{{ tag }}
						</el-tag>
					</el-descriptions-item>
					<el-descriptions-item label="Assignees" v-if="currentProject.assignees.length">
						<span v-for="(assigneeId, index) in currentProject.assignees" :key="assigneeId">
							{{ getAssigneeName(assigneeId) }}{{ index < currentProject.assignees.length - 1 ? ', ' : '' }}
						</span>
					</el-descriptions-item>
					<el-descriptions-item label="Start" v-if="currentProject.start_time">
						{{ useDateFormat(currentProject.start_time, 'DD/MM/YY HH:mm', { locales: 'fr-FR' }) }}
					</el-descriptions-item>
					<el-descriptions-item label="End" v-if="currentProject.end_time">
						{{ useDateFormat(currentProject.end_time, 'DD/MM/YY HH:mm', { locales: 'fr-FR' }) }}
					</el-descriptions-item>
				</el-descriptions>
			</div>
			<div class="columns-container">
				<kanban-column v-for="column in columns" :key="column.id" :column="column"
					:tasks="getTasksByColumnId(column.id)"
					@add-task="addTask" @edit-task="editTask" @delete-task="confirmDeleteTask" @move-task="handleMoveTask" />
			</div>
		</div>
		<DialogTask :task="editingTask" @save="editingTask = undefined" />
		<el-dialog v-model="showDeleteDialog" title="Confirm Delete" width="30%">
			<span>{{ projectDeleteMessage }}</span>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="taskToDelete = undefined">Cancel</el-button>
					<el-button type="danger" @click="deleteTask">Delete</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<style scoped>
.kanban-container {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.kanban-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	flex-shrink: 0;
}

.el-descriptions,
.el-descriptions :deep(tbody),
.el-descriptions :deep(tr) {
    width: 100%;
}

.el-descriptions :deep(table) {
	table-layout:fixed;
	width: 100%;
}

:deep(.el-descriptions__label) {
    width: 120px;
}

:deep(.el-descriptions__content) {
    width: calc(50%);
}

.kanban-board {
	overflow-x: auto;
	flex-grow: 1;
	min-height: 0;
}

.columns-container {
	display: flex;
	gap: 20px;
	flex-grow: 1;
	padding-bottom: 10px;
}

.empty-state {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 300px;
}

.loading-container,
.error-container {
	padding: 40px;
	text-align: center;
}
</style>