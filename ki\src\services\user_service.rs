use anyhow::Result;
use chrono::Utc;
use rs_firebase_admin_sdk::auth::token::jwt::JWToken;
use tracing::{info, warn};

use crate::db::{
    connection::Database,
    models::{NewUser, UpdateUser, User},
    repositories::UserRepository,
};

/// User service for handling automatic user creation and management
pub struct UserService {
    user_repo: UserRepository,
}

impl UserService {
    /// Create a new user service
    pub fn new(db: Database) -> Self {
        Self {
            user_repo: UserRepository::new(db),
        }
    }

    /// Ensure user exists for Firebase authentication
    /// Creates a new user if one doesn't exist, or updates last_login if it does
    pub async fn ensure_user_exists(&self, firebase_token: &JWToken) -> Result<User> {
        let firebase_user_id = &firebase_token.critical_claims.sub;
        
        // Try to find existing user by Firebase user ID
        match self.user_repo.get_by_user_id(firebase_user_id).await? {
            Some(existing_user) => {
                // User exists, update last_login
                info!("User {} already exists, updating last login", firebase_user_id);
                
                let update_user = UpdateUser {
                    email: None,
                    display_name: None,
                    photo_url: None,
                    last_login: Some(Utc::now()),
                };
                
                self.user_repo.update(existing_user.id, update_user).await
            }
            None => {
                // User doesn't exist, create new user from Firebase token
                info!("Creating new user for Firebase user {}", firebase_user_id);
                
                let new_user = self.create_user_from_firebase_token(firebase_token)?;
                self.user_repo.create(new_user).await
            }
        }
    }

    /// Create a NewUser from Firebase token claims
    fn create_user_from_firebase_token(&self, firebase_token: &JWToken) -> Result<NewUser> {
        let firebase_user_id = firebase_token.critical_claims.sub.clone();
        
        // Extract email from Firebase token
        let email = firebase_token.all_claims
            .get("email")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string())
            .unwrap_or_else(|| {
                warn!("No email found in Firebase token for user {}", firebase_user_id);
                // Use a placeholder email if none is provided
                format!("{}@firebase.user", firebase_user_id)
            });

        // Extract display name from Firebase token
        let display_name = firebase_token.all_claims
            .get("name")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        // Extract photo URL from Firebase token
        let photo_url = firebase_token.all_claims
            .get("picture")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        Ok(NewUser {
            user_id: firebase_user_id,
            email,
            display_name,
            photo_url,
        })
    }

    /// Create a user manually (without Firebase ID initially)
    pub async fn create_manual_user(&self, email: String, display_name: Option<String>, photo_url: Option<String>) -> Result<User> {
        // Generate a temporary user_id for manual users
        // This can be updated later when they authenticate with Firebase
        let temp_user_id = format!("manual_{}", uuid::Uuid::new_v4());
        
        let new_user = NewUser {
            user_id: temp_user_id,
            email,
            display_name,
            photo_url,
        };

        self.user_repo.create(new_user).await
    }

    /// Update a manual user's Firebase ID when they authenticate
    pub async fn link_firebase_user(&self, _user_id: uuid::Uuid, firebase_user_id: String) -> Result<User> {
        // Check if the Firebase user ID is already in use
        if let Some(_existing_user) = self.user_repo.get_by_user_id(&firebase_user_id).await? {
            return Err(anyhow::anyhow!("Firebase user ID {} is already linked to another user", firebase_user_id));
        }

        // Note: We need to add a method to update the user_id field
        // For now, we'll return an error indicating this functionality needs to be implemented
        Err(anyhow::anyhow!("Firebase user linking not yet implemented - requires database schema update"))
    }

    /// Get user repository for direct access when needed
    pub fn get_user_repo(&self) -> &UserRepository {
        &self.user_repo
    }
}
