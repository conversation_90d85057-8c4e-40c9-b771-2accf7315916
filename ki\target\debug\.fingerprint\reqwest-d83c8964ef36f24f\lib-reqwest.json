{"rustc": 10895048813736897673, "features": "[\"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 447021486742529345, "path": 9795262057244925075, "deps": [[40386456601120721, "percent_encoding", false, 15857642291582845488], [95042085696191081, "ipnet", false, 8145823000316458768], [784494742817713399, "tower_service", false, 5023113295579284643], [1906322745568073236, "pin_project_lite", false, 13483621581492765273], [2517136641825875337, "sync_wrapper", false, 17561740311626693908], [3150220818285335163, "url", false, 6546836770925716605], [3722963349756955755, "once_cell", false, 12825410154089437232], [5138218615291878843, "tokio", false, 9271106830573487278], [5695049318159433696, "tower", false, 2238454123613380397], [5986029879202738730, "log", false, 5644347006075319131], [7620660491849607393, "futures_core", false, 14165407329339629864], [9010263965687315507, "http", false, 13780035913158987009], [9689903380558560274, "serde", false, 9156359934049382691], [10229185211513642314, "mime", false, 15775420741624474642], [10595802073777078462, "hyper_util", false, 3927148230416752761], [10629569228670356391, "futures_util", false, 12506543492908000869], [11957360342995674422, "hyper", false, 17281462051268656735], [12186126227181294540, "tokio_native_tls", false, 9687420763534955807], [13077212702700853852, "base64", false, 557792220295956855], [13868379202103418305, "h2", false, 14574861471420470128], [14084095096285906100, "http_body", false, 13164054679762100366], [14564311161534545801, "encoding_rs", false, 7819603559686564352], [15032952994102373905, "rustls_pemfile", false, 1674500747841939761], [15367738274754116744, "serde_json", false, 2753059415649191222], [15697835491348449269, "windows_registry", false, 9213841031964343445], [16066129441945555748, "bytes", false, 14214947457689394275], [16542808166767769916, "serde_urlencoded", false, 17557067676169356801], [16785601910559813697, "native_tls_crate", false, 3444236120226141774], [16900715236047033623, "http_body_util", false, 11044969495969053557], [18273243456331255970, "hyper_tls", false, 10443693131557967794]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-d83c8964ef36f24f\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}