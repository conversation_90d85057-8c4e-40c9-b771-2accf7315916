<script setup lang="ts">
import { computed, ref, type PropType } from 'vue';
import { useKanbanStore } from '@/stores/kanbanStore';
import TaskStatusIcon from './TaskStatusIcon.vue';
import TaskActions from './TaskActions.vue';
import type { Task, TaskStatus } from '@/types';

const props = defineProps({
    taskId: String as PropType<string>
});
const emit = defineEmits<{
        (e: 'edit-task', task: Task): void;
        (e: 'delete-task', taskId: string): void;
}>();

const kanbanStore = useKanbanStore();
const subtasks = computed(() => props.taskId ? kanbanStore.getSubTasks(props.taskId) : []);
const newSubtaskName = ref('');

function changeTaskStatus(taskId: string, newStatus: TaskStatus) {
    kanbanStore.moveTask(taskId, newStatus, subtasks.value.filter(subtask => subtask.status === newStatus).length);
}
function createNewSubtask() {
    if (!newSubtaskName.value.trim()) return;
    kanbanStore.createTask({
        name: newSubtaskName.value,
        status: 'todo',
        position: subtasks.value.length,
        parent_task_id: props.taskId,
        tags: [],
        assignees: [],
        start_time: undefined,
        end_time: undefined,
        blocking: [],
        blocked_by: [],
    });
    newSubtaskName.value = '';
}
</script>

<template>
    <el-card class="subtasks-card">
        <ul class="subtasks flex vertical">
            <li class="subtask flex" v-for="subtask in subtasks" :key="subtask.id">
                <el-select class="subtask-status" v-model="subtask.status" :suffix-icon="null" @change="changeTaskStatus(subtask.id, subtask.status)">
                    <template #label="{ value }">
                        <TaskStatusIcon :status="value" />
                    </template>
                    <el-option v-for="column in kanbanStore.columns" :key="column.id" :label="column.name" :value="column.id">
                        <div class="status-option flex">
                            <TaskStatusIcon :status="column.id" />
                            <span>{{ column.name }}</span>
                        </div>
                    </el-option>
                </el-select>
                <router-link class="subtask-name" :to="subtask.id">{{ subtask.name }}</router-link>
                <TaskActions @editTask="emit('edit-task', subtask)" @deleteTask="emit('delete-task', subtask.id)" />
            </li>
            <li>
                <el-input v-model="newSubtaskName" placeholder="New subtask" @keyup.enter="createNewSubtask" />
            </li>
        </ul>
    </el-card>
</template>

<style scoped>
.subtasks {
    padding: 0px;
    margin: 0px;
    gap: 8px
}

.subtasks > li {
    list-style: none;
}

.subtask {
    gap: 8px;
    align-items: center;
}

.subtask-status {
	width: 24px;
    height: 24px;
    min-width: 24px;
    min-height: 24px;
}

.status-option {
    gap: 8px;
	align-items: center;
}

.subtask-name {
	flex-grow: 1;
}

:deep(.el-select__wrapper) {
    padding: 0px;
	width: 24px;
    height: 24px;
    min-height: 24px;
    gap: 0;
    border-radius: 12px;
}

:deep(.el-select__selection) {
    width: 100%;
}

:deep(.el-select__selected-item) {
    width: 100%;
    text-align: center;
}

.subtask :deep(.el-icon) {
    height: 24px;
    margin: 5px;
}

#subtask-new {
    margin: 0px;
}
</style>