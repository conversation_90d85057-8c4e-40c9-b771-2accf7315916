{"rustc": 10895048813736897673, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 16046190976464274511, "deps": [[1988483478007900009, "unicode_ident", false, 13360760724070541663], [12410540580958238005, "proc_macro2", false, 10639316160010260521], [17990358020177143287, "quote", false, 14279578803395008540]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-ce16736090afe21d\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}