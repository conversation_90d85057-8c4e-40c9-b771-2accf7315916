{"rustc": 10895048813736897673, "features": "[\"_rt-tokio\", \"_sqlite\", \"_tls-rustls-ring-webpki\", \"chrono\", \"default\", \"derive\", \"json\", \"macros\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_sqlite\", \"_tls-native-tls\", \"_tls-rustls-aws-lc-rs\", \"_tls-rustls-ring-native-roots\", \"_tls-rustls-ring-webpki\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"derive\", \"ipnet\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlite-unbundled\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 2175425913391121376, "path": 11495661155069631410, "deps": [[530211389790465181, "hex", false, 14601131202669845500], [3150220818285335163, "url", false, 17567045014819899217], [3405707034081185165, "dotenvy", false, 14968311870511842935], [3722963349756955755, "once_cell", false, 15354684742482638160], [4783846006802799581, "sqlx_sqlite", false, 13142682755091814437], [5138218615291878843, "tokio", false, 7724235904046310901], [5236433071915784494, "sha2", false, 6046574198266981968], [8986759836770526006, "syn", false, 13717353883845142581], [9689903380558560274, "serde", false, 4111864293651695815], [12170264697963848012, "either", false, 3074432237505480162], [12261610614527126074, "tempfile", false, 15130170547367518511], [12410540580958238005, "proc_macro2", false, 10639316160010260521], [13077543566650298139, "heck", false, 11301959593663298675], [15367738274754116744, "serde_json", false, 18095941757884197139], [16423736323724667376, "sqlx_core", false, 7790652359597707370], [17990358020177143287, "quote", false, 14279578803395008540]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-core-b61a25790fd47cb6\\dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}