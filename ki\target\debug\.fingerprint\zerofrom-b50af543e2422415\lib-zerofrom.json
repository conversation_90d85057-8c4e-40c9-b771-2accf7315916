{"rustc": 10895048813736897673, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 2225463790103693989, "path": 16798977816863026250, "deps": [[4022439902832367970, "zerofrom_derive", false, 8249009870697984639]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-b50af543e2422415\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}