use anyhow::Result;
use uuid::Uuid;

use crate::db::{
    connection::{Database, DatabaseConnection},
    models::{EntityType, NewPermission, Permission, PermissionLevel, ResourceType, UpdatePermission},
};

/// Permission repository
pub struct PermissionRepository {
    db: Database,
}

impl PermissionRepository {
    /// Create a new permission repository
    pub fn new(db: Database) -> Self {
        Self { db }
    }

    /// Create a new permission
    pub async fn create(&self, new_permission: NewPermission) -> Result<Permission> {
        let id = Uuid::new_v4();

        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let entity_type_str = match new_permission.entity_type {
                    EntityType::User => "user",
                    EntityType::Agent => "agent",
                };
                let resource_type_str = match new_permission.resource_type {
                    ResourceType::Task => "task",
                    ResourceType::Project => "project",
                    ResourceType::User => "user",
                    ResourceType::Agent => "agent",
                    ResourceType::System => "system",
                };
                let permission_level_str = match new_permission.permission_level {
                    PermissionLevel::Read => "read",
                    PermissionLevel::Write => "write",
                    PermissionLevel::Admin => "admin",
                };

                let permission = sqlx::query_as::<_, Permission>(
                    r#"
                    INSERT INTO permissions (id, entity_type, entity_id, resource_type, resource_id, permission_level, granted_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    RETURNING id, entity_type, entity_id, resource_type, resource_id, permission_level, granted_by, created_at
                    "#
                )
                .bind(id)
                .bind(entity_type_str)
                .bind(&new_permission.entity_id)
                .bind(resource_type_str)
                .bind(&new_permission.resource_id)
                .bind(permission_level_str)
                .bind(&new_permission.granted_by)
                .fetch_one(pool)
                .await?;

                Ok(permission)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let permission = sqlx::query_as::<_, Permission>(
                    r#"
                    INSERT INTO permissions (id, entity_type, entity_id, resource_type, resource_id, permission_level, granted_by)
                    VALUES ($1, $2, $3, $4, $5, $6, $7)
                    RETURNING id, entity_type, entity_id, resource_type, resource_id, permission_level, granted_by, created_at
                    "#
                )
                .bind(id)
                .bind(&new_permission.entity_type)
                .bind(&new_permission.entity_id)
                .bind(&new_permission.resource_type)
                .bind(&new_permission.resource_id)
                .bind(&new_permission.permission_level)
                .bind(&new_permission.granted_by)
                .fetch_one(pool)
                .await?;

                Ok(permission)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get all permissions
    pub async fn get_all(&self) -> Result<Vec<Permission>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let permissions = sqlx::query_as::<_, Permission>(
                    r#"
                    SELECT id, entity_type, entity_id, resource_type, resource_id, permission_level, granted_by, created_at
                    FROM permissions
                    ORDER BY created_at DESC
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(permissions)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let permissions = sqlx::query_as::<_, Permission>(
                    r#"
                    SELECT id, entity_type, entity_id, resource_type, resource_id, permission_level, granted_by, created_at
                    FROM permissions
                    ORDER BY created_at DESC
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(permissions)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get permissions for a specific entity
    pub async fn get_by_entity(&self, entity_type: EntityType, entity_id: &str) -> Result<Vec<Permission>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let entity_type_str = match entity_type {
                    EntityType::User => "user",
                    EntityType::Agent => "agent",
                };

                let permissions = sqlx::query_as::<_, Permission>(
                    r#"
                    SELECT id, entity_type, entity_id, resource_type, resource_id, permission_level, granted_by, created_at
                    FROM permissions
                    WHERE entity_type = ? AND entity_id = ?
                    ORDER BY created_at DESC
                    "#
                )
                .bind(entity_type_str)
                .bind(entity_id)
                .fetch_all(pool)
                .await?;

                Ok(permissions)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let permissions = sqlx::query_as::<_, Permission>(
                    r#"
                    SELECT id, entity_type, entity_id, resource_type, resource_id, permission_level, granted_by, created_at
                    FROM permissions
                    WHERE entity_type = $1 AND entity_id = $2
                    ORDER BY created_at DESC
                    "#
                )
                .bind(&entity_type)
                .bind(entity_id)
                .fetch_all(pool)
                .await?;

                Ok(permissions)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get permissions for a specific resource
    pub async fn get_by_resource(&self, resource_type: ResourceType, resource_id: Option<&str>) -> Result<Vec<Permission>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let resource_type_str = match resource_type {
                    ResourceType::Task => "task",
                    ResourceType::Project => "project",
                    ResourceType::User => "user",
                    ResourceType::Agent => "agent",
                    ResourceType::System => "system",
                };

                let permissions = if let Some(res_id) = resource_id {
                    sqlx::query_as::<_, Permission>(
                        r#"
                        SELECT id, entity_type, entity_id, resource_type, resource_id, permission_level, granted_by, created_at
                        FROM permissions
                        WHERE resource_type = ? AND resource_id = ?
                        ORDER BY created_at DESC
                        "#
                    )
                    .bind(resource_type_str)
                    .bind(res_id)
                    .fetch_all(pool)
                    .await?
                } else {
                    sqlx::query_as::<_, Permission>(
                        r#"
                        SELECT id, entity_type, entity_id, resource_type, resource_id, permission_level, granted_by, created_at
                        FROM permissions
                        WHERE resource_type = ? AND resource_id IS NULL
                        ORDER BY created_at DESC
                        "#
                    )
                    .bind(resource_type_str)
                    .fetch_all(pool)
                    .await?
                };

                Ok(permissions)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let permissions = if let Some(res_id) = resource_id {
                    sqlx::query_as::<_, Permission>(
                        r#"
                        SELECT id, entity_type, entity_id, resource_type, resource_id, permission_level, granted_by, created_at
                        FROM permissions
                        WHERE resource_type = $1 AND resource_id = $2
                        ORDER BY created_at DESC
                        "#
                    )
                    .bind(&resource_type)
                    .bind(res_id)
                    .fetch_all(pool)
                    .await?
                } else {
                    sqlx::query_as::<_, Permission>(
                        r#"
                        SELECT id, entity_type, entity_id, resource_type, resource_id, permission_level, granted_by, created_at
                        FROM permissions
                        WHERE resource_type = $1 AND resource_id IS NULL
                        ORDER BY created_at DESC
                        "#
                    )
                    .bind(&resource_type)
                    .fetch_all(pool)
                    .await?
                };

                Ok(permissions)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Check if an entity has a specific permission
    pub async fn has_permission(
        &self,
        entity_type: EntityType,
        entity_id: &str,
        resource_type: ResourceType,
        resource_id: Option<&str>,
        required_level: PermissionLevel,
    ) -> Result<bool> {
        let permissions = self.get_by_entity(entity_type, entity_id).await?;

        for permission in permissions {
            // Check if this permission applies to the resource
            if permission.resource_type == resource_type {
                // Check if resource_id matches (None means global permission)
                if permission.resource_id.as_deref() == resource_id || permission.resource_id.is_none() {
                    // Check if permission level is sufficient
                    let has_sufficient_level = match required_level {
                        PermissionLevel::Read => true, // Any permission level includes read
                        PermissionLevel::Write => matches!(permission.permission_level, PermissionLevel::Write | PermissionLevel::Admin),
                        PermissionLevel::Admin => matches!(permission.permission_level, PermissionLevel::Admin),
                    };

                    if has_sufficient_level {
                        return Ok(true);
                    }
                }
            }
        }

        Ok(false)
    }

    /// Update a permission
    pub async fn update(&self, id: Uuid, update_permission: UpdatePermission) -> Result<Permission> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let permission_level_str = match update_permission.permission_level {
                    PermissionLevel::Read => "read",
                    PermissionLevel::Write => "write",
                    PermissionLevel::Admin => "admin",
                };

                let permission = sqlx::query_as::<_, Permission>(
                    r#"
                    UPDATE permissions SET permission_level = ?
                    WHERE id = ?
                    RETURNING id, entity_type, entity_id, resource_type, resource_id, permission_level, granted_by, created_at
                    "#
                )
                .bind(permission_level_str)
                .bind(id)
                .fetch_one(pool)
                .await?;

                Ok(permission)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let permission = sqlx::query_as::<_, Permission>(
                    r#"
                    UPDATE permissions SET permission_level = $2
                    WHERE id = $1
                    RETURNING id, entity_type, entity_id, resource_type, resource_id, permission_level, granted_by, created_at
                    "#
                )
                .bind(id)
                .bind(&update_permission.permission_level)
                .fetch_one(pool)
                .await?;

                Ok(permission)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Delete a permission
    pub async fn delete(&self, id: Uuid) -> Result<()> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                sqlx::query("DELETE FROM permissions WHERE id = ?")
                    .bind(id)
                    .execute(pool)
                    .await?;

                Ok(())
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                sqlx::query!("DELETE FROM permissions WHERE id = $1", id)
                    .execute(pool)
                    .await?;

                Ok(())
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }
}
