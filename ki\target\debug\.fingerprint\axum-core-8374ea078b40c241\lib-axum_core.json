{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 2241668132362809309, "path": 10925507480384047280, "deps": [[784494742817713399, "tower_service", false, 5023113295579284643], [1906322745568073236, "pin_project_lite", false, 13483621581492765273], [1999399154011168049, "rustversion", false, 12095854159049431513], [2517136641825875337, "sync_wrapper", false, 17561740311626693908], [7712452662827335977, "tower_layer", false, 14302084456410726127], [9010263965687315507, "http", false, 13780035913158987009], [10229185211513642314, "mime", false, 15775420741624474642], [10629569228670356391, "futures_util", false, 12506543492908000869], [11946729385090170470, "async_trait", false, 15670438612964185507], [14084095096285906100, "http_body", false, 13164054679762100366], [16066129441945555748, "bytes", false, 14214947457689394275], [16900715236047033623, "http_body_util", false, 11044969495969053557]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\axum-core-8374ea078b40c241\\dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}