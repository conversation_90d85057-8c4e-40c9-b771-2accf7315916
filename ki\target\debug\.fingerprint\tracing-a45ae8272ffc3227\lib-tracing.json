{"rustc": 10895048813736897673, "features": "[\"attributes\", \"default\", \"log\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 6483948657570918867, "path": 2050980551974143417, "deps": [[1906322745568073236, "pin_project_lite", false, 14069272232144782415], [2967683870285097694, "tracing_attributes", false, 12968725748617320773], [5986029879202738730, "log", false, 12303517477126591968], [11033263105862272874, "tracing_core", false, 8383499247928318094]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-a45ae8272ffc3227\\dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}