use anyhow::Result;
use uuid::Uuid;
use sqlx::types::<PERSON><PERSON>;

use crate::db::{
    connection::{Database, DatabaseConnection},
    models::{Agent, NewAgent, UpdateAgent},
};

/// Agent repository
pub struct AgentRepository {
    db: Database,
}

impl AgentRepository {
    /// Create a new agent repository
    pub fn new(db: Database) -> Self {
        Self { db }
    }

    /// Create a new agent
    pub async fn create(&self, new_agent: NewAgent) -> Result<Agent> {
        let id = Uuid::new_v4();
        let is_active = new_agent.is_active.unwrap_or(true);

        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let agent = sqlx::query_as::<_, Agent>(
                    r#"
                    INSERT INTO agents (id, name, description, parameters, created_by, is_active)
                    VALUES (?, ?, ?, ?, ?, ?)
                    RETURNING id, name, description, parameters, created_by, created_at, updated_at, is_active
                    "#
                )
                .bind(id)
                .bind(&new_agent.name)
                .bind(&new_agent.description)
                .bind(Json(&new_agent.parameters))
                .bind(&new_agent.created_by)
                .bind(is_active)
                .fetch_one(pool)
                .await?;

                Ok(agent)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let agent = sqlx::query_as::<_, Agent>(
                    r#"
                    INSERT INTO agents (id, name, description, parameters, created_by, is_active)
                    VALUES ($1, $2, $3, $4, $5, $6)
                    RETURNING id, name, description, parameters, created_by, created_at, updated_at, is_active
                    "#
                )
                .bind(id)
                .bind(&new_agent.name)
                .bind(&new_agent.description)
                .bind(Json(&new_agent.parameters))
                .bind(&new_agent.created_by)
                .bind(is_active)
                .fetch_one(pool)
                .await?;

                Ok(agent)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get all agents
    pub async fn get_all(&self) -> Result<Vec<Agent>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let agents = sqlx::query_as::<_, Agent>(
                    r#"
                    SELECT id, name, description, parameters, created_by, created_at, updated_at, is_active
                    FROM agents
                    ORDER BY created_at DESC
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(agents)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let agents = sqlx::query_as::<_, Agent>(
                    r#"
                    SELECT id, name, description, parameters, created_by, created_at, updated_at, is_active
                    FROM agents
                    ORDER BY created_at DESC
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(agents)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get active agents only
    pub async fn get_active(&self) -> Result<Vec<Agent>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let agents = sqlx::query_as::<_, Agent>(
                    r#"
                    SELECT id, name, description, parameters, created_by, created_at, updated_at, is_active
                    FROM agents
                    WHERE is_active = TRUE
                    ORDER BY created_at DESC
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(agents)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let agents = sqlx::query_as::<_, Agent>(
                    r#"
                    SELECT id, name, description, parameters, created_by, created_at, updated_at, is_active
                    FROM agents
                    WHERE is_active = TRUE
                    ORDER BY created_at DESC
                    "#
                )
                .fetch_all(pool)
                .await?;

                Ok(agents)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get an agent by ID
    pub async fn get_by_id(&self, id: Uuid) -> Result<Option<Agent>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let agent = sqlx::query_as::<_, Agent>(
                    r#"
                    SELECT id, name, description, parameters, created_by, created_at, updated_at, is_active
                    FROM agents
                    WHERE id = ?
                    "#
                )
                .bind(id)
                .fetch_optional(pool)
                .await?;

                Ok(agent)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let agent = sqlx::query_as::<_, Agent>(
                    r#"
                    SELECT id, name, description, parameters, created_by, created_at, updated_at, is_active
                    FROM agents
                    WHERE id = $1
                    "#
                )
                .bind(id)
                .fetch_optional(pool)
                .await?;

                Ok(agent)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Get agents created by a specific user
    pub async fn get_by_creator(&self, created_by: &str) -> Result<Vec<Agent>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let agents = sqlx::query_as::<_, Agent>(
                    r#"
                    SELECT id, name, description, parameters, created_by, created_at, updated_at, is_active
                    FROM agents
                    WHERE created_by = ?
                    ORDER BY created_at DESC
                    "#
                )
                .bind(created_by)
                .fetch_all(pool)
                .await?;

                Ok(agents)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let agents = sqlx::query_as::<_, Agent>(
                    r#"
                    SELECT id, name, description, parameters, created_by, created_at, updated_at, is_active
                    FROM agents
                    WHERE created_by = $1
                    ORDER BY created_at DESC
                    "#
                )
                .bind(created_by)
                .fetch_all(pool)
                .await?;

                Ok(agents)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Update an agent
    pub async fn update(&self, id: Uuid, update_agent: UpdateAgent) -> Result<Agent> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                // For SQLite, we'll use a simpler approach with individual field updates
                // Get current agent first
                let current = self.get_by_id(id).await?.ok_or_else(|| anyhow::anyhow!("Agent not found"))?;

                let name = update_agent.name.unwrap_or(current.name);
                let description = update_agent.description.or(current.description);
                let parameters = update_agent.parameters.unwrap_or(current.parameters.0);
                let is_active = update_agent.is_active.unwrap_or(current.is_active);

                let agent = sqlx::query_as::<_, Agent>(
                    r#"
                    UPDATE agents SET
                        name = ?,
                        description = ?,
                        parameters = ?,
                        is_active = ?
                    WHERE id = ?
                    RETURNING id, name, description, parameters, created_by, created_at, updated_at, is_active
                    "#
                )
                .bind(&name)
                .bind(&description)
                .bind(Json(&parameters))
                .bind(is_active)
                .bind(id)
                .fetch_one(pool)
                .await?;

                Ok(agent)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let agent = sqlx::query_as::<_, Agent>(
                    r#"
                    UPDATE agents SET
                        name = COALESCE($2, name),
                        description = COALESCE($3, description),
                        parameters = COALESCE($4, parameters),
                        is_active = COALESCE($5, is_active)
                    WHERE id = $1
                    RETURNING id, name, description, parameters, created_by, created_at, updated_at, is_active
                    "#
                )
                .bind(id)
                .bind(&update_agent.name)
                .bind(&update_agent.description)
                .bind(update_agent.parameters.as_ref().map(Json))
                .bind(&update_agent.is_active)
                .fetch_one(pool)
                .await?;

                Ok(agent)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    /// Delete an agent
    pub async fn delete(&self, id: Uuid) -> Result<()> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                sqlx::query("DELETE FROM agents WHERE id = ?")
                    .bind(id)
                    .execute(pool)
                    .await?;

                Ok(())
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                sqlx::query!("DELETE FROM agents WHERE id = $1", id)
                    .execute(pool)
                    .await?;

                Ok(())
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }
}
